<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.2.5</version>
		<relativePath/>
	</parent>
	
	<groupId>com.salaryadvance</groupId>
	<artifactId>salary-advance-pvcb</artifactId>
	<version>1.1-SNAPSHOT</version>
	<packaging>pom</packaging>
	<name>salary-advance-pvcb</name>
	<description>Parent POM for Salary Advance PVCB project</description>
	
	<properties>
		<revision>1.1-SNAPSHOT</revision>
		<java.version>21</java.version>
		<spring-cloud.version>2023.0.1</spring-cloud.version>
		<keycloak-spring-security-adapter.version>25.0.3</keycloak-spring-security-adapter.version>
		<opencsv.version>5.9</opencsv.version>
		<redisson.version>3.27.2</redisson.version>
		<poi.version>5.2.5</poi.version>
		<sonar.organization>pvcb-ungluong</sonar.organization>
		<sonar.host.url>https://sonarcloud.io</sonar.host.url>
	</properties>
	
	<modules>
		<module>common-library</module>
		<module>user</module>
		<module>portal</module>
		<module>salary-advance</module>
		<module>job-schedule</module>
		<module>integration</module>
		<module>file</module>
		<module>webhook</module>
	</modules>
	
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>org.keycloak</groupId>
				<artifactId>keycloak-spring-security-adapter</artifactId>
				<version>${keycloak-spring-security-adapter.version}</version>
			</dependency>
			<dependency>
				<groupId>org.keycloak</groupId>
				<artifactId>keycloak-spring-boot-starter</artifactId>
				<version>${keycloak-spring-security-adapter.version}</version>
			</dependency>
			<dependency>
				<groupId>org.keycloak</groupId>
				<artifactId>keycloak-admin-client</artifactId>
				<version>${keycloak-spring-security-adapter.version}</version>
			</dependency>
			<dependency>
				<groupId>com.opencsv</groupId>
				<artifactId>opencsv</artifactId>
				<version>${opencsv.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi</artifactId>
				<version>${poi.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml</artifactId>
				<version>${poi.version}</version>
			</dependency>
			<dependency>
				<groupId>org.redisson</groupId>
				<artifactId>redisson-spring-boot-starter</artifactId>
				<version>${redisson.version}</version>
			</dependency>
			<dependency>
				<groupId>org.redisson</groupId>
				<artifactId>redisson-spring-data-22</artifactId>
				<version>${redisson.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
	
	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-compiler-plugin</artifactId>
					<configuration>
						<source>${java.version}</source>
						<target>${java.version}</target>
					</configuration>
				</plugin>
				<plugin>
					<groupId>org.jacoco</groupId>
					<artifactId>jacoco-maven-plugin</artifactId>
					<version>0.8.11</version>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>
</project>
