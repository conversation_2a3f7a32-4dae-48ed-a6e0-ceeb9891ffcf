{"id": "33b41cc1-ede9-4db5-ad07-5e63bfcde185", "realm": "pronexus_dev", "displayName": "", "displayNameHtml": "", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 1800, "accessTokenLifespanForImplicitFlow": 604800, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 3600, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 1200, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "none", "registrationAllowed": true, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": true, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxTemporaryLockouts": 0, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "131ad322-13d6-442d-b436-7f4777320736", "name": "default-roles-pronexus_dev", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["offline_access", "uma_authorization"], "client": {"pronexus_dev": ["uma_protection"], "account": ["manage-account", "view-profile"]}}, "clientRole": false, "containerId": "33b41cc1-ede9-4db5-ad07-5e63bfcde185", "attributes": {}}, {"id": "7cb19eb6-e653-4acc-bcc0-a2bae6710182", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "33b41cc1-ede9-4db5-ad07-5e63bfcde185", "attributes": {}}, {"id": "93c054b8-92a8-468b-9763-ab438d59d3b0", "name": "SYSTEM_ADMIN", "description": "", "composite": false, "clientRole": false, "containerId": "33b41cc1-ede9-4db5-ad07-5e63bfcde185", "attributes": {}}, {"id": "8c0d1ff6-9083-43f5-8162-7b92c69c328b", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "33b41cc1-ede9-4db5-ad07-5e63bfcde185", "attributes": {}}, {"id": "52ec7e68-8bbb-411e-908b-b12f190c652d", "name": "PORTAL_ADMIN", "description": "", "composite": false, "clientRole": false, "containerId": "33b41cc1-ede9-4db5-ad07-5e63bfcde185", "attributes": {}}], "client": {"pronexus_dev": [{"id": "b04d83a4-b105-46b1-bb86-1e0f2ee7fd70", "name": "PORTAL_ADMIN", "description": "", "composite": false, "clientRole": true, "containerId": "6938d616-b627-4ee8-8c47-e09c2901ec6b", "attributes": {}}, {"id": "49bb32b7-29bb-4dd6-bfa8-45e53d4c0bb9", "name": "SYSTEM_ADMIN", "description": "", "composite": false, "clientRole": true, "containerId": "6938d616-b627-4ee8-8c47-e09c2901ec6b", "attributes": {}}, {"id": "19cc8c9b-1a9f-4f5f-948b-81e5a8525c7f", "name": "uma_protection", "composite": false, "clientRole": true, "containerId": "6938d616-b627-4ee8-8c47-e09c2901ec6b", "attributes": {}}], "realm-management": [{"id": "8ad80f9b-aacc-491a-88ce-f6e558c7f434", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "221a1823-3619-4587-bfe5-e1378661e64a", "attributes": {}}, {"id": "abd1b62f-cadc-4344-a897-3ff0cc07df78", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "221a1823-3619-4587-bfe5-e1378661e64a", "attributes": {}}, {"id": "5f8e5478-4ff0-424d-803f-b208399502d4", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "221a1823-3619-4587-bfe5-e1378661e64a", "attributes": {}}, {"id": "e8d2eb4e-8f4c-4758-9185-c51746259e95", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "221a1823-3619-4587-bfe5-e1378661e64a", "attributes": {}}, {"id": "e3f035ae-4066-4ab3-886b-8d394fa7e9ce", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "221a1823-3619-4587-bfe5-e1378661e64a", "attributes": {}}, {"id": "20aa40f7-0277-4bea-9f3a-24f1720936a0", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["query-groups", "view-authorization", "view-clients", "manage-events", "manage-realm", "manage-clients", "query-users", "manage-authorization", "create-client", "manage-users", "view-identity-providers", "query-clients", "view-users", "query-realms", "impersonation", "view-events", "view-realm", "manage-identity-providers"]}}, "clientRole": true, "containerId": "221a1823-3619-4587-bfe5-e1378661e64a", "attributes": {}}, {"id": "fae54c5c-eb3f-45e9-99c2-65899bb1eed6", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "221a1823-3619-4587-bfe5-e1378661e64a", "attributes": {}}, {"id": "2f93a9c6-bd33-4e29-bd29-5f5643a085a7", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "221a1823-3619-4587-bfe5-e1378661e64a", "attributes": {}}, {"id": "4aa818ad-7e7d-49da-8464-8fde82a315bd", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "221a1823-3619-4587-bfe5-e1378661e64a", "attributes": {}}, {"id": "94ad67a3-b468-4fe8-98a7-9476df726fce", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "221a1823-3619-4587-bfe5-e1378661e64a", "attributes": {}}, {"id": "f79b1352-ef3c-498f-96d8-8b4e4027da2b", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "221a1823-3619-4587-bfe5-e1378661e64a", "attributes": {}}, {"id": "4555ffe5-5137-4d41-b093-dc7c32939a88", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "221a1823-3619-4587-bfe5-e1378661e64a", "attributes": {}}, {"id": "22adf8dc-d69a-4e65-9813-7cff6baf173b", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "221a1823-3619-4587-bfe5-e1378661e64a", "attributes": {}}, {"id": "6b362e2f-2454-4ed4-8c17-24b538038390", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "221a1823-3619-4587-bfe5-e1378661e64a", "attributes": {}}, {"id": "241f439c-144c-4b3c-ae17-37388b3d15aa", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "221a1823-3619-4587-bfe5-e1378661e64a", "attributes": {}}, {"id": "efd9102e-17bd-444c-9325-c9ba7b9646c2", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "221a1823-3619-4587-bfe5-e1378661e64a", "attributes": {}}, {"id": "89b6d63d-2ba0-4369-9548-3accffc352c8", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "221a1823-3619-4587-bfe5-e1378661e64a", "attributes": {}}, {"id": "7238f2e6-3209-46f6-b679-466c2dcdb413", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "221a1823-3619-4587-bfe5-e1378661e64a", "attributes": {}}, {"id": "9a823a67-48a3-49b8-9af7-c6df89739294", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "221a1823-3619-4587-bfe5-e1378661e64a", "attributes": {}}], "security-admin-console": [], "admin-cli": [], "account-console": [], "broker": [{"id": "3ea3b194-d1d7-436d-9059-cfba233aaffc", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "71bddfee-cef5-474f-98a0-80ba221bd5fd", "attributes": {}}], "keycloak-angular": [], "pronexus_dev_frontend": [], "account": [{"id": "53c8bb96-28b3-4897-80e9-d8a1491b3498", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "738a5724-dda1-4423-a213-40488d145942", "attributes": {}}, {"id": "e07b8f36-856e-459f-ad05-7e71669e13cd", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "738a5724-dda1-4423-a213-40488d145942", "attributes": {}}, {"id": "66b321bf-ffd6-4706-89ca-bf2077d5d901", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "738a5724-dda1-4423-a213-40488d145942", "attributes": {}}, {"id": "1d108e02-6913-4332-84c3-e5b2b669ba76", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "738a5724-dda1-4423-a213-40488d145942", "attributes": {}}, {"id": "2b2e9658-e020-4ee8-90be-5fea295f337d", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "738a5724-dda1-4423-a213-40488d145942", "attributes": {}}, {"id": "16636a1d-fd2c-4873-905a-bb9139f58cfb", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "738a5724-dda1-4423-a213-40488d145942", "attributes": {}}, {"id": "3541c745-9e1b-47f5-9c8e-f6c428ae1ba4", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "738a5724-dda1-4423-a213-40488d145942", "attributes": {}}, {"id": "017211d3-c824-46c8-baf7-a2779294d2db", "name": "view-groups", "description": "${role_view-groups}", "composite": false, "clientRole": true, "containerId": "738a5724-dda1-4423-a213-40488d145942", "attributes": {}}]}}, "groups": [], "defaultRole": {"id": "131ad322-13d6-442d-b436-7f4777320736", "name": "default-roles-pronexus_dev", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "33b41cc1-ede9-4db5-ad07-5e63bfcde185"}, "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpPolicyCodeReusable": false, "otpSupportedApplications": ["totpAppFreeOTPName", "totpAppGoogleName", "totpAppMicrosoftAuthenticatorName"], "localizationTexts": {}, "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyExtraOrigins": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "webAuthnPolicyPasswordlessExtraOrigins": [], "users": [{"id": "009d7949-a726-40b0-bbe9-616fc024a4cf", "username": "service-account-pronexus_dev", "emailVerified": false, "createdTimestamp": *************, "enabled": true, "totp": false, "serviceAccountClientId": "pronexus_dev", "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-pronexus_dev"], "clientRoles": {"realm-management": ["realm-admin"], "pronexus_dev": ["uma_protection"], "account": ["manage-account", "view-profile"]}, "notBefore": 0, "groups": []}], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account", "view-groups"]}]}, "clients": [{"id": "738a5724-dda1-4423-a213-40488d145942", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/pronexus_dev/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/pronexus_dev/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "false", "post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}, {"id": "fd034ce8-ea3c-42c0-a99d-853be34567cc", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/pronexus_dev/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/pronexus_dev/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "false", "post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "3505dd80-b5a5-4070-baf1-fe1be5e7c490", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}, {"id": "e17009d0-5723-4699-8793-7beac71d523b", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "false", "client.use.lightweight.access.token.enabled": "true"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}, {"id": "71bddfee-cef5-474f-98a0-80ba221bd5fd", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "true"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}, {"id": "ec12b78d-7317-4edb-ad9f-6bc8ddf4c7ee", "clientId": "keycloak-angular", "name": "Keycloak Angular Sandbox", "description": "", "rootUrl": "http://localhost:4200", "adminUrl": "", "baseUrl": "http://localhost:4200", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["http://localhost:4200/*"], "webOrigins": ["http://localhost:4200"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"access.token.lifespan": "60", "post.logout.redirect.uris": "http://localhost:4200/*", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false", "use.refresh.tokens": "true", "tls-client-certificate-bound-access-tokens": "false", "realm_client": "false", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "acr.loa.map": "{}", "require.pushed.authorization.requests": "false", "display.on.consent.screen": "false", "client.session.max.lifespan": "1800", "client.session.idle.timeout": "300", "token.response.type.bearer.lower-case": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "6938d616-b627-4ee8-8c47-e09c2901ec6b", "clientId": "pronexus_dev", "name": "Pronexus", "description": "", "rootUrl": "", "adminUrl": "*", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["*"], "webOrigins": ["*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": true, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "authorizationServicesEnabled": true, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"client.secret.creation.time": "**********", "client.introspection.response.allow.jwt.claim.enabled": "false", "login_theme": "keycloak.v2", "post.logout.redirect.uris": "*", "oauth2.device.authorization.grant.enabled": "false", "use.jwks.url": "false", "backchannel.logout.revoke.offline.tokens": "false", "use.refresh.tokens": "true", "realm_client": "false", "oidc.ciba.grant.enabled": "false", "client.use.lightweight.access.token.enabled": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "tls.client.certificate.bound.access.tokens": "false", "require.pushed.authorization.requests": "false", "acr.loa.map": "{}", "display.on.consent.screen": "false", "token.response.type.bearer.lower-case": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "31276e00-9258-43aa-a79e-819f36d42812", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "client_id", "id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "claim.name": "client_id", "jsonType.label": "String"}}, {"id": "c597a787-06d0-4e8b-8d19-d2e35f2770d3", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}, {"id": "9fa37998-b0ce-48a8-b7d4-30f9ff90cc04", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"], "authorizationSettings": {"allowRemoteResourceManagement": true, "policyEnforcementMode": "PERMISSIVE", "resources": [{"name": "<PERSON><PERSON><PERSON> m<PERSON> quản lý kh<PERSON>c", "type": "urn:pronexus_dev_frontend:resources:ql_khac", "ownerManagedAccess": false, "displayName": "<PERSON><PERSON><PERSON> m<PERSON> quản lý kh<PERSON>c", "attributes": {"parentId": ["0"]}, "uris": ["/*"], "icon_uri": "solar:file-text-line-duotone"}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý nhân sự", "type": "urn:pronexus_dev_frontend:resources:ql_nhansu", "ownerManagedAccess": false, "displayName": "<PERSON><PERSON><PERSON><PERSON> lý nhân sự", "attributes": {"parentId": ["a8728cbc-ef3d-48bf-ba81-c1321be1ba36"], "route": ["/employee"]}, "uris": ["/*"], "icon_uri": "solar:database-line-duotone"}, {"name": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> l<PERSON>", "type": "urn:pronexus_dev_frontend:resources:ql_ungluong", "ownerManagedAccess": false, "displayName": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> l<PERSON>", "attributes": {"isMenu": ["true"], "parentId": ["a8728cbc-ef3d-48bf-ba81-c1321be1ba36"], "route": ["/employee/list-salary-advance"]}, "uris": ["/*"], "icon_uri": "solar:database-line-duotone"}, {"name": "<PERSON><PERSON><PERSON> c<PERSON>o <PERSON> lư<PERSON> chi tiết", "type": "urn:pronexus_dev_frontend:resources:baocao", "ownerManagedAccess": false, "displayName": "<PERSON><PERSON><PERSON> c<PERSON>o <PERSON> lư<PERSON> chi tiết", "attributes": {"isMenu": ["true"], "parentId": ["2dff6e97-e33e-4352-ba24-1d8013126e33"], "route": ["/reports/detail-salary-advance"]}, "uris": ["/*"], "scopes": [{"name": "IMPORT"}, {"name": "ADD"}, {"name": "SEND_NOTIFICATION"}, {"name": "VIEW"}], "icon_uri": "solar:chart-square-line-duotone"}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý đối so<PERSON>t", "type": "urn:pronexus_dev_frontend:resources:ql_doisoat", "ownerManagedAccess": false, "displayName": "<PERSON><PERSON><PERSON><PERSON> lý đối so<PERSON>t", "attributes": {"parentId": ["a8728cbc-ef3d-48bf-ba81-c1321be1ba36"]}, "uris": ["/*"], "icon_uri": "solar:database-line-duotone"}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý hạn mức", "type": "urn:pronexus_dev_frontend:resources:ql_hanmuc", "ownerManagedAccess": false, "displayName": "<PERSON><PERSON><PERSON><PERSON> lý hạn mức", "attributes": {"parentId": ["a8728cbc-ef3d-48bf-ba81-c1321be1ba36"]}, "uris": ["/*"], "icon_uri": "solar:database-line-duotone"}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý giao d<PERSON>ch", "type": "urn:pronexus_dev_frontend:resources:ql_giaodich", "ownerManagedAccess": false, "displayName": "<PERSON><PERSON><PERSON><PERSON> lý giao d<PERSON>ch", "attributes": {"isMenu": ["true"], "parentId": ["a8728cbc-ef3d-48bf-ba81-c1321be1ba36"]}, "uris": ["/*"], "icon_uri": "solar:database-line-duotone"}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý ng<PERSON><PERSON> dùng", "ownerManagedAccess": false, "displayName": "<PERSON><PERSON><PERSON><PERSON> lý ng<PERSON><PERSON> dùng", "attributes": {"isMenu": ["true"], "parentId": ["0"], "route": ["/system-management/user"]}, "uris": ["/*"], "scopes": [{"name": "ADD"}, {"name": "SEND_NOTIFICATION"}, {"name": "DELETE"}, {"name": "EDIT"}, {"name": "VIEW"}, {"name": "EXPORT"}], "icon_uri": "solar:user-plus-rounded-line-duotone"}, {"name": "<PERSON><PERSON><PERSON> c<PERSON>o <PERSON>ng lư<PERSON>ng tổng hợp", "type": "urn:pronexus_dev_frontend:resources:baocao", "ownerManagedAccess": false, "displayName": "<PERSON><PERSON><PERSON> c<PERSON>o <PERSON>ng lư<PERSON>ng tổng hợp", "attributes": {"isMenu": ["true"], "parentId": ["2dff6e97-e33e-4352-ba24-1d8013126e33"], "route": ["/reports/summary-salary-advance"]}, "uris": ["/*"], "scopes": [{"name": "IMPORT"}, {"name": "ADD"}, {"name": "SEND_NOTIFICATION"}, {"name": "VIEW"}], "icon_uri": "solar:chart-square-line-duotone"}, {"name": "<PERSON><PERSON>o c<PERSON>o đ<PERSON> so<PERSON>t", "type": "urn:pronexus_dev_frontend:resources:baocao", "ownerManagedAccess": false, "displayName": "<PERSON><PERSON>o c<PERSON>o đ<PERSON> so<PERSON>t", "attributes": {"isMenu": ["true"], "parentId": ["2dff6e97-e33e-4352-ba24-1d8013126e33"], "route": ["/reports/review-salary-advance-report"]}, "uris": ["/*"], "scopes": [{"name": "IMPORT"}, {"name": "ADD"}, {"name": "SEND_NOTIFICATION"}, {"name": "VIEW"}], "icon_uri": "solar:chart-square-line-duotone"}, {"name": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "type": "urn:pronexus_dev_frontend:resources:ql_doitac", "ownerManagedAccess": false, "displayName": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "attributes": {"isMenu": ["true"], "parentId": ["0"], "route": ["/salary"]}, "uris": [], "scopes": [{"name": "IMPORT"}, {"name": "SEND_NOTIFICATION"}, {"name": "DELETE"}, {"name": "EDIT"}, {"name": "VIEW"}, {"name": "EXPORT"}], "icon_uri": "solar:chart-square-line-duotone"}, {"name": "Báo cáo tra soát", "type": "urn:pronexus_dev_frontend:resources:baocao", "ownerManagedAccess": false, "displayName": "Báo cáo tra soát", "attributes": {"isMenu": ["true"], "parentId": ["2dff6e97-e33e-4352-ba24-1d8013126e33"], "route": ["reports/inspection-salary-advance"]}, "uris": ["/*"], "scopes": [{"name": "IMPORT"}, {"name": "ADD"}, {"name": "SEND_NOTIFICATION"}, {"name": "VIEW"}], "icon_uri": "solar:chart-square-line-duotone"}, {"name": "<PERSON><PERSON><PERSON> cáo thu phí dịch vụ", "type": "urn:pronexus_dev_frontend:resources:baocao", "ownerManagedAccess": false, "displayName": "<PERSON><PERSON><PERSON> cáo thu phí dịch vụ", "attributes": {"isMenu": ["true"], "parentId": ["2dff6e97-e33e-4352-ba24-1d8013126e33"], "route": ["/reports/service-fee-report"]}, "uris": ["/*"], "scopes": [{"name": "IMPORT"}, {"name": "ADD"}, {"name": "SEND_NOTIFICATION"}, {"name": "VIEW"}], "icon_uri": "solar:chart-square-line-duotone"}, {"name": "Trang chủ", "ownerManagedAccess": false, "displayName": "Trang chủ", "attributes": {"isMenu": ["true"], "sort": ["0"]}, "uris": ["/*"], "scopes": [{"name": "IMPORT"}]}, {"name": "Báo cáo", "ownerManagedAccess": false, "displayName": "Báo cáo", "attributes": {"isMenu": ["true"], "sort": ["1"]}, "uris": ["/*"], "scopes": [{"name": "IMPORT"}]}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý đối tác", "type": "urn:pronexus_dev_frontend:resources:ql_doitac", "ownerManagedAccess": false, "displayName": "<PERSON><PERSON><PERSON><PERSON> lý đối tác", "attributes": {"isMenu": ["true"], "parentId": ["0"], "route": ["/partner"]}, "uris": ["/*"], "scopes": [{"name": "IMPORT"}, {"name": "ADD"}, {"name": "DELETE"}, {"name": "EDIT"}, {"name": "VIEW"}, {"name": "EXPORT"}], "icon_uri": "solar:chart-square-line-duotone"}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý chấm công", "ownerManagedAccess": false, "displayName": "<PERSON><PERSON><PERSON><PERSON> lý chấm công", "attributes": {"isMenu": ["true"], "parentId": ["0"], "route": ["/attendance-employee"]}, "uris": [], "icon_uri": "solar:database-line-duotone"}, {"name": "<PERSON>áo c<PERSON>o ho<PERSON>", "ownerManagedAccess": false, "displayName": "<PERSON>áo c<PERSON>o ho<PERSON>", "attributes": {"isMenu": ["true"]}, "uris": ["/*"], "scopes": [{"name": "IMPORT"}]}, {"name": "Báo cáo quá hạn", "ownerManagedAccess": false, "displayName": "Báo cáo quá hạn", "attributes": {"isMenu": ["true"]}, "uris": ["/"]}], "policies": [{"name": "Default Policy", "description": "A policy that grants access only for users within this realm", "type": "js", "logic": "POSITIVE", "decisionStrategy": "AFFIRMATIVE", "config": {"code": "// by default, grants any permission associated with this policy\n$evaluation.grant();\n"}}, {"name": "System Admin Policy", "description": "", "type": "role", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"fetchRoles": "true", "roles": "[{\"id\":\"SYSTEM_ADMIN\",\"required\":false}]"}}, {"name": "Portal Admin Policy", "description": "", "type": "role", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"fetchRoles": "true", "roles": "[{\"id\":\"PORTAL_ADMIN\",\"required\":true}]"}}, {"name": "<PERSON><PERSON><PERSON><PERSON> xem trang chủ", "description": "", "type": "resource", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"defaultResourceType": "urn:pronexus_dev_frontend:resources:dashboard", "resources": "[\"Trang chủ\"]", "applyPolicies": "[\"System Admin Policy\"]"}}, {"name": "Default Permission", "description": "A permission that applies to the default resource type", "type": "resource", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"defaultResourceType": "urn:pronexus_dev:resources:default", "applyPolicies": "[\"Default Policy\"]"}}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý đối tác", "description": "", "type": "resource", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"defaultResourceType": "urn:pronexus_dev_frontend:resources:ql_doitac", "resources": "[\"<PERSON><PERSON>ản lý đối tác\"]", "applyPolicies": "[\"System Admin Policy\"]"}}, {"name": "<PERSON><PERSON><PERSON><PERSON> quản lý lư<PERSON>", "description": "", "type": "resource", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"defaultResourceType": "urn:pronexus_dev_frontend:resources:ql_luong", "applyPolicies": "[\"System Admin Policy\"]"}}, {"name": "<PERSON><PERSON><PERSON><PERSON> quản lý chấm công", "description": "", "type": "resource", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"defaultResourceType": "", "resources": "[\"<PERSON><PERSON><PERSON><PERSON> lý chấm công\"]", "applyPolicies": "[\"System Admin Policy\"]"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "resource", "logic": "POSITIVE", "decisionStrategy": "AFFIRMATIVE", "config": {"defaultResourceType": "urn:pronexus_dev_frontend:resources:baocao", "resources": "[\"Báo cáo\"]", "applyPolicies": "[\"System Admin Policy\"]"}}, {"name": "<PERSON><PERSON><PERSON><PERSON> mục quản lý khác", "description": "", "type": "resource", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"defaultResourceType": "urn:pronexus_dev_frontend:resources:ql_khac", "resources": "[\"<PERSON><PERSON><PERSON> mục quản lý khác\"]", "applyPolicies": "[\"System Admin Policy\"]"}}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý hạn mức", "description": "", "type": "resource", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"defaultResourceType": "urn:pronexus_dev_frontend:resources:ql_hanmuc", "resources": "[\"<PERSON><PERSON><PERSON><PERSON> lý hạn mức\"]", "applyPolicies": "[\"System Admin Policy\"]"}}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý giao d<PERSON>ch", "description": "", "type": "resource", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"defaultResourceType": "urn:pronexus_dev_frontend:resources:ql_giaodich", "resources": "[\"<PERSON><PERSON><PERSON>n lý giao dịch\"]", "applyPolicies": "[\"System Admin Policy\"]"}}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý nhân sự", "description": "", "type": "resource", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"defaultResourceType": "urn:pronexus_dev_frontend:resources:ql_nhansu", "resources": "[\"Q<PERSON>ản lý nhân sự\"]", "applyPolicies": "[\"System Admin Policy\"]"}}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý đối soát", "description": "", "type": "resource", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"defaultResourceType": "urn:pronexus_dev_frontend:resources:ql_doisoat", "resources": "[\"<PERSON><PERSON>ản lý đối soát\"]", "applyPolicies": "[\"System Admin Policy\"]"}}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON> l<PERSON>", "description": "", "type": "resource", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"defaultResourceType": "urn:pronexus_dev_frontend:resources:ql_ungluong", "resources": "[\"<PERSON><PERSON><PERSON><PERSON> lý <PERSON>ng lương\"]", "applyPolicies": "[\"System Admin Policy\"]"}}], "scopes": [{"name": "ADD"}, {"name": "EDIT"}, {"name": "DELETE"}, {"name": "VIEW"}, {"name": "EXPORT"}, {"name": "IMPORT"}, {"name": "SEND_NOTIFICATION"}], "decisionStrategy": "UNANIMOUS"}}, {"id": "d1a320f9-4451-4da5-9b9d-1aa007a2ae91", "clientId": "pronexus_dev_frontend", "name": "pronexus_dev_frontend", "description": "pronexus_dev_frontend", "rootUrl": "https://admin.pronexus.vn", "adminUrl": "https://admin.pronexus.vn", "baseUrl": "https://admin.pronexus.vn", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-authenticator", "redirectUris": ["https://identity.pronexus.vn/*", "https://admin.pronexus.vn/*", "http://localhost:4200/*", "https://localhost:4200/*"], "webOrigins": ["https://admin.pronexus.vn", "https://localhost:4200", "https://identity.pronexus.vn", "http://localhost:4200"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"access.token.lifespan": "900", "client.secret.creation.time": "**********", "client.introspection.response.allow.jwt.claim.enabled": "false", "login_theme": "keycloak.v2", "post.logout.redirect.uris": "https://admin.pronexus.vn/*##https://identity.pronexus.vn/*##https://localhost:4200/*##http://localhost:4200/*", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false", "use.refresh.tokens": "false", "realm_client": "false", "oidc.ciba.grant.enabled": "false", "client.use.lightweight.access.token.enabled": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "client.offline.session.idle.timeout": "600", "tls.client.certificate.bound.access.tokens": "false", "require.pushed.authorization.requests": "false", "acr.loa.map": "{}", "display.on.consent.screen": "false", "client.session.max.lifespan": "600", "token.response.type.bearer.lower-case": "false", "client.session.idle.timeout": "600"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "8cfb009e-46e5-4252-89c1-99ad38df3065", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}, {"id": "338f9b05-e5d1-4403-9e80-b901c1668203", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "client_id", "id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "claim.name": "client_id", "jsonType.label": "String"}}, {"id": "02f12f9c-2dcb-485e-a07e-fafa9caa8400", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}, {"id": "221a1823-3619-4587-bfe5-e1378661e64a", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "true"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}, {"id": "0ad3afd8-d659-47a9-a551-0c257ad92f3c", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/pronexus_dev/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/pronexus_dev/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "false", "client.use.lightweight.access.token.enabled": "true", "post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "21d2b068-9dc6-44b6-90d3-5887aae83c3b", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}], "clientScopes": [{"id": "5d3f5e30-4a0c-402a-ab5d-d2fa93f89945", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${phoneScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "95fbb7b3-5ed3-4097-afca-f1f893072775", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}, {"id": "046fc036-7e15-462e-b9dd-c20414987cef", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}]}, {"id": "78b04137-563b-4a44-a397-1a7d1ed6ed2d", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "consent.screen.text": "", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "bdda54c4-c711-4310-82f7-e297bf9d49b4", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "access.token.claim": "true"}}]}, {"id": "bb98fc01-663b-42ed-ac69-a5df10b79064", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "027bfbd0-4616-49c4-8a14-21bf531df5d2", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "consent.screen.text": "${rolesScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "606567c6-0b60-4aed-801a-c622a71a0e13", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "access.token.claim": "true"}}, {"id": "71a8f013-1303-420c-83b5-7a65d8e23c90", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "introspection.token.claim": "true", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "55a9e6e3-a189-47fe-ac94-99356099090d", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "introspection.token.claim": "true", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}]}, {"id": "ed775b57-722f-4345-9023-a0e82a042f17", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${addressScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "a52ac338-2432-4573-8119-16d8b15567e4", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "introspection.token.claim": "true", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "e0640070-7100-4405-b446-8d3b8c5df4d0", "name": "basic", "description": "OpenID Connect scope for add all basic claims to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "dad62ffa-f168-4db8-8a23-f0ab16df2eff", "name": "auth_time", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "AUTH_TIME", "id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "claim.name": "auth_time", "jsonType.label": "long"}}, {"id": "f44f4ff7-3947-4e0e-8f82-d39818cbe3d3", "name": "sub", "protocol": "openid-connect", "protocolMapper": "oidc-sub-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "access.token.claim": "true"}}]}, {"id": "a362aca0-92aa-4303-ab83-a8911845aab3", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${profileScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "13ebecf6-06f6-4dd9-997a-81797331a9e9", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "1327677c-6332-4860-af8f-f6160f470958", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "1cc08ab8-cf76-4512-a843-3be49a84d0d7", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "f8d57b48-d818-4cbc-b0a4-78b0d174e285", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "2ecff1a5-3009-4e2a-92de-2677f309c74c", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "long"}}, {"id": "6eb3f0fd-6666-47cf-8ff3-474163a98efb", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "ba33fd9f-4367-4546-93c8-3d97720afeff", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "f8c24f6c-d99c-4f2e-8cfc-589ee8b0a548", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "672257f7-15e3-448e-af0f-55ca89759f73", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "e6760d58-176e-4f74-b5d9-f8820d0082b9", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "a3b87094-e5f5-4ca1-9630-1aba39440860", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "996c9b26-cfb1-47b7-9ed0-d9f283aeb97e", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "cb0dbf7d-c193-4ac8-9cbd-b5a8e3d0c378", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "3862afc9-9186-48dd-8bfd-eb77f8d39d57", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}]}, {"id": "bc8d3af0-41c2-4442-94e6-ab79c7f8f3db", "name": "acr", "description": "OpenID Connect scope for add acr (authentication context class reference) to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "10bbe3d5-378a-4d05-b45d-b1e3f1309c34", "name": "acr loa level", "protocol": "openid-connect", "protocolMapper": "oidc-acr-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true"}}]}, {"id": "5d804fd1-8781-4302-901a-2377bd88fd2d", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${emailScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "1e2e9a2f-d8eb-40d0-a760-a3082541d502", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}, {"id": "3535ccbc-2887-4bed-bba1-1adf4808be63", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}]}, {"id": "9cb06ddc-e841-4bf6-8707-d1411108328d", "name": "organization", "description": "Additional claims about the organization a subject belongs to", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${organizationScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "a698d49e-58ab-47ac-a2e8-234db7ba391e", "name": "organization", "protocol": "openid-connect", "protocolMapper": "oidc-organization-membership-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "claim.name": "organization", "jsonType.label": "String", "multivalued": "true"}}]}, {"id": "07bc1c2e-4044-440c-bc7d-315724590531", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "2fb4aa84-8a21-4814-8fe1-4d084465a879", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "0adbacd9-a073-49cb-85e2-d0d0795fdb97", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "11f02a44-43fc-47f7-bd58-77037c6df4d2", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}, {"id": "b4ccb617-89f7-4d1b-a66a-6fc0feb1c791", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}]}, {"id": "e71449a0-4a22-4c14-a8a5-eec9733a182a", "name": "saml_organization", "description": "Organization Membership", "protocol": "saml", "attributes": {"display.on.consent.screen": "false"}, "protocolMappers": [{"id": "d7b9e0f1-6687-4799-abbe-e1643c73be53", "name": "organization", "protocol": "saml", "protocolMapper": "saml-organization-membership-mapper", "consentRequired": false, "config": {}}]}], "defaultDefaultClientScopes": ["role_list", "saml_organization", "profile", "email", "roles", "web-origins", "acr", "basic"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt", "organization"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "referrerPolicy": "no-referrer", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self' http://localhost:4200 https://localhost:4200 https://admin.pronexus.vn; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=********; includeSubDomains"}, "smtpServer": {}, "loginTheme": "keycloak.v2", "accountTheme": "keycloak.v3", "adminTheme": "keycloak.v2", "emailTheme": "", "eventsEnabled": true, "eventsListeners": ["jboss-logging"], "enabledEventTypes": ["UPDATE_CONSENT_ERROR", "SEND_RESET_PASSWORD", "GRANT_CONSENT", "VERIFY_PROFILE_ERROR", "UPDATE_TOTP", "REMOVE_TOTP", "REVOKE_GRANT", "LOGIN_ERROR", "CLIENT_LOGIN", "RESET_PASSWORD_ERROR", "UPDATE_CREDENTIAL", "IMPERSONATE_ERROR", "CODE_TO_TOKEN_ERROR", "CUSTOM_REQUIRED_ACTION", "OAUTH2_DEVICE_CODE_TO_TOKEN_ERROR", "RESTART_AUTHENTICATION", "UPDATE_PROFILE_ERROR", "IMPERSONATE", "LOGIN", "UPDATE_PASSWORD_ERROR", "OAUTH2_DEVICE_VERIFY_USER_CODE", "CLIENT_INITIATED_ACCOUNT_LINKING", "USER_DISABLED_BY_PERMANENT_LOCKOUT", "OAUTH2_EXTENSION_GRANT", "REMOVE_CREDENTIAL_ERROR", "TOKEN_EXCHANGE", "REGISTER", "LOGOUT", "AUTHREQID_TO_TOKEN", "DELETE_ACCOUNT_ERROR", "CLIENT_REGISTER", "IDENTITY_PROVIDER_LINK_ACCOUNT", "USER_DISABLED_BY_TEMPORARY_LOCKOUT", "UPDATE_PASSWORD", "DELETE_ACCOUNT", "FEDERATED_IDENTITY_LINK_ERROR", "CLIENT_DELETE", "IDENTITY_PROVIDER_FIRST_LOGIN", "VERIFY_EMAIL", "CLIENT_DELETE_ERROR", "CLIENT_LOGIN_ERROR", "RESTART_AUTHENTICATION_ERROR", "REMOVE_FEDERATED_IDENTITY_ERROR", "EXECUTE_ACTIONS", "TOKEN_EXCHANGE_ERROR", "PERMISSION_TOKEN", "FEDERATED_IDENTITY_OVERRIDE_LINK", "SEND_IDENTITY_PROVIDER_LINK_ERROR", "UPDATE_CREDENTIAL_ERROR", "EXECUTE_ACTION_TOKEN_ERROR", "SEND_VERIFY_EMAIL", "OAUTH2_EXTENSION_GRANT_ERROR", "OAUTH2_DEVICE_AUTH", "EXECUTE_ACTIONS_ERROR", "REMOVE_FEDERATED_IDENTITY", "OAUTH2_DEVICE_CODE_TO_TOKEN", "IDENTITY_PROVIDER_POST_LOGIN", "IDENTITY_PROVIDER_LINK_ACCOUNT_ERROR", "FEDERATED_IDENTITY_OVERRIDE_LINK_ERROR", "UPDATE_EMAIL", "OAUTH2_DEVICE_VERIFY_USER_CODE_ERROR", "REGISTER_ERROR", "REVOKE_GRANT_ERROR", "LOGOUT_ERROR", "UPDATE_EMAIL_ERROR", "EXECUTE_ACTION_TOKEN", "CLIENT_UPDATE_ERROR", "UPDATE_PROFILE", "AUTHREQID_TO_TOKEN_ERROR", "INVITE_ORG_ERROR", "FEDERATED_IDENTITY_LINK", "CLIENT_REGISTER_ERROR", "INVITE_ORG", "SEND_VERIFY_EMAIL_ERROR", "SEND_IDENTITY_PROVIDER_LINK", "RESET_PASSWORD", "CLIENT_INITIATED_ACCOUNT_LINKING_ERROR", "OAUTH2_DEVICE_AUTH_ERROR", "REMOVE_CREDENTIAL", "UPDATE_CONSENT", "REMOVE_TOTP_ERROR", "VERIFY_EMAIL_ERROR", "SEND_RESET_PASSWORD_ERROR", "CLIENT_UPDATE", "IDENTITY_PROVIDER_POST_LOGIN_ERROR", "CUSTOM_REQUIRED_ACTION_ERROR", "UPDATE_TOTP_ERROR", "CODE_TO_TOKEN", "VERIFY_PROFILE", "GRANT_CONSENT_ERROR", "IDENTITY_PROVIDER_FIRST_LOGIN_ERROR"], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [], "identityProviderMappers": [], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "495e567d-940e-4eca-84a0-ac316acc412a", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "ef9f62d1-71e2-448e-bffd-f571a291078d", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-usermodel-property-mapper", "saml-role-list-mapper", "saml-user-property-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-full-name-mapper", "oidc-address-mapper", "saml-user-attribute-mapper", "oidc-usermodel-attribute-mapper"]}}, {"id": "0fc46a83-b901-4393-a829-ccbd233769c8", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "f2f6a487-f65c-4835-ad8f-f5dd1ae28f6d", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "b4207e42-00f2-43fc-b163-b14750e28a88", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "854584fd-3a3d-4505-b693-04fd3f35b8db", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "83a0ba63-053b-4f4e-b19f-5febe603ed78", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "2f2a7124-a3c0-4bf8-b3c5-32908878a860", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-role-list-mapper", "oidc-full-name-mapper", "oidc-usermodel-attribute-mapper", "oidc-usermodel-property-mapper", "saml-user-attribute-mapper", "saml-user-property-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-address-mapper"]}}], "org.keycloak.userprofile.UserProfileProvider": [{"id": "89d8c250-dfe5-4001-9d96-f79b300a1c9a", "providerId": "declarative-user-profile", "subComponents": {}, "config": {"kc.user.profile.config": ["{\"attributes\":[{\"name\":\"username\",\"displayName\":\"${username}\",\"validations\":{\"length\":{\"min\":3,\"max\":255},\"username-prohibited-characters\":{},\"up-username-not-idn-homograph\":{}},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false},{\"name\":\"email\",\"displayName\":\"${email}\",\"validations\":{\"email\":{},\"length\":{\"max\":255}},\"required\":{\"roles\":[\"user\"]},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false},{\"name\":\"firstName\",\"displayName\":\"${firstName}\",\"validations\":{\"length\":{\"max\":255},\"person-name-prohibited-characters\":{}},\"required\":{\"roles\":[\"user\"]},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false},{\"name\":\"lastName\",\"displayName\":\"${lastName}\",\"validations\":{\"length\":{\"max\":255},\"person-name-prohibited-characters\":{}},\"required\":{\"roles\":[\"user\"]},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false}],\"groups\":[{\"name\":\"user-metadata\",\"displayHeader\":\"User metadata\",\"displayDescription\":\"Attributes, which refer to user metadata\"}],\"unmanagedAttributePolicy\":\"ENABLED\"}"]}}], "org.keycloak.keys.KeyProvider": [{"id": "15a22636-8757-49ba-bb1c-24fa637f4957", "name": "rsa-enc-generated", "providerId": "rsa-enc-generated", "subComponents": {}, "config": {"priority": ["100"], "algorithm": ["RSA-OAEP"]}}, {"id": "a559d1bd-265d-4242-b9d7-7288f7ec8a15", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"priority": ["100"]}}, {"id": "95f6e5d4-92bd-4d94-b9aa-da7f5255cb5a", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"priority": ["100"]}}, {"id": "********-1d56-4e67-8b51-b4b3ab9d624a", "name": "hmac-generated-hs512", "providerId": "hmac-generated", "subComponents": {}, "config": {"priority": ["100"], "algorithm": ["HS512"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "ae8ef7fd-b72b-4928-bc47-8b8ed069a1d1", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false}]}, {"id": "06f4046d-e879-4cbd-b753-5d73439acbfa", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "1a440cbc-20ec-4bee-8f12-5011ba0b14bb", "alias": "Browser - Conditional Organization", "description": "Flow to determine if the organization identity-first login is to be used", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "organization", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "4a1add53-4f6d-46ab-b6a7-0cb72526ba12", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "088b179f-2a37-47dd-8adf-40aa22c26e4d", "alias": "First Broker Login - Conditional Organization", "description": "Flow to determine if the authenticator that adds organization members is to be used", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "idp-add-organization-member", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "619ecef3-d4f1-4cbb-8e47-e9c3ee4f4290", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "89e187e3-5027-4b9d-9b8c-9b5ddd9a8491", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Account verification options", "userSetupAllowed": false}]}, {"id": "7dde8dfd-7fa0-419d-a2cc-ad2ef7e18d1f", "alias": "Organization", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 10, "autheticatorFlow": true, "flowAlias": "Browser - Conditional Organization", "userSetupAllowed": false}]}, {"id": "6b71f7e9-8806-400c-934f-095907871ab3", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "5e06bb02-1d9f-41cd-b591-16ebf1a0cbc9", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false}]}, {"id": "0cda182f-0f50-4a0b-ba9f-791cf1d6ec67", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false}]}, {"id": "aa67a777-e2f8-45c4-a25f-5dbc57c90708", "alias": "browser", "description": "Browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "DISABLED", "priority": 26, "autheticatorFlow": true, "flowAlias": "Organization", "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": true, "flowAlias": "forms", "userSetupAllowed": false}]}, {"id": "5cbe3405-76f3-43fd-bdd9-f73ce2e6eaaf", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "7b195a4e-1b53-4b03-bc54-4de15243d5fb", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false}]}, {"id": "66c4cbf1-9f80-455f-a534-df10b14d4089", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "d7ebd7cb-f7a3-4061-8b84-71b0b8f80d9e", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "User creation or linking", "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 50, "autheticatorFlow": true, "flowAlias": "First Broker Login - Conditional Organization", "userSetupAllowed": false}]}, {"id": "b8a1696c-01b1-4f76-970e-eb92882c0baa", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false}]}, {"id": "10ba2e31-0260-4948-afae-230e2ec412c6", "alias": "registration", "description": "Registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": true, "flowAlias": "registration form", "userSetupAllowed": false}]}, {"id": "e7e546b0-0ba5-4dad-8052-a9aebcdd3f3b", "alias": "registration form", "description": "Registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-terms-and-conditions", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 70, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "5015dae0-4b91-4274-b735-6951c99b6a95", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false}]}, {"id": "20579d22-916c-4612-bb77-ecb2f07ceeaa", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}], "authenticatorConfig": [{"id": "7effe991-432a-4f98-aba4-bd1b44c3acb6", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "d74dffaa-09a3-4f6a-a060-9053417b676b", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": false, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "TERMS_AND_CONDITIONS", "name": "Terms and Conditions", "providerId": "TERMS_AND_CONDITIONS", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "webauthn-register", "name": "Webauthn Register", "providerId": "webauthn-register", "enabled": true, "defaultAction": false, "priority": 70, "config": {}}, {"alias": "webauthn-register-passwordless", "name": "Webauthn Register Passwordless", "providerId": "webauthn-register-passwordless", "enabled": true, "defaultAction": false, "priority": 80, "config": {}}, {"alias": "VERIFY_PROFILE", "name": "Verify Profile", "providerId": "VERIFY_PROFILE", "enabled": true, "defaultAction": false, "priority": 90, "config": {}}, {"alias": "delete_credential", "name": "Delete Credential", "providerId": "delete_credential", "enabled": true, "defaultAction": false, "priority": 100, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "firstBrokerLoginFlow": "first broker login", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "cibaAuthRequestedUserHint": "login_hint", "oauth2DevicePollingInterval": "5", "clientOfflineSessionMaxLifespan": "0", "clientSessionIdleTimeout": "0", "actionTokenGeneratedByUserLifespan.verify-email": "", "actionTokenGeneratedByUserLifespan.idp-verify-account-via-email": "", "clientOfflineSessionIdleTimeout": "0", "actionTokenGeneratedByUserLifespan.execute-actions": "", "cibaInterval": "5", "realmReusableOtpCode": "false", "cibaExpiresIn": "120", "oauth2DeviceCodeLifespan": "1200", "parRequestUriLifespan": "60", "clientSessionMaxLifespan": "0", "frontendUrl": "https://identity.pronexus.vn", "organizationsEnabled": "false", "acr.loa.map": "{}", "shortVerificationUri": "", "actionTokenGeneratedByUserLifespan.reset-credentials": ""}, "keycloakVersion": "26.0.2", "userManagedAccessAllowed": true, "organizationsEnabled": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}