spring.application.name=salary-advance
server.servlet.context-path=/salary-advance

logging.config=classpath:logback-spring.xml

server.port=8089
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=https://identity.pronexus.vn/realms/pronexus_dev/protocol/openid-connect/certs

spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.datasource.driver-class-name=org.postgresql.Driver
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
#spring.datasource.url=*****************************************
spring.datasource.url=******************************************************
spring.datasource.username=postgres
spring.datasource.password=Thanhnx@123$
spring.datasource.hikari.minimumIdle=5
spring.datasource.hikari.maximumPoolSize=200
spring.datasource.hikari.idleTimeout=30000
spring.datasource.hikari.poolName=HikariCP
spring.datasource.hikari.maxLifetime=2000000
spring.datasource.hikari.connectionTimeout=30000

keycloak.realm=pronexus_dev
keycloak.auth-server-url=https://identity.pronexus.vn

#portal.service.url=https://api.pronexus.vn/portal
portal.service.url=http://localhost:8000/portal

integration.service.url=https://api.pronexus.vn/integration
#integration.service.url=http://localhost:8088/integration

cors.allowed-origins=*
spring.main.allow-bean-definition-overriding=true
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.redisson=debug

manual.review.errors=417,418,991,992,996,68,500,400
