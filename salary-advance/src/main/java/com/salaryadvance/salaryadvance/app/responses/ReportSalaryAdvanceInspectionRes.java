package com.salaryadvance.salaryadvance.app.responses;

import lombok.Data;

@Data
public class ReportSalaryAdvanceInspectionRes {
    private String employeeId;
    private String employeeCode;
    private String employeeName;
    private Integer quantity;
    private Long advanceAmountTotal;
    private String destinationAccount;
    private String destinationBankName;
    private Long partnerId;
    private Long departmentId;
    private String departmentName;
    private String departmentCode;
//    private RequestInspectionStatusnStatus status;

    public ReportSalaryAdvanceInspectionRes(String employeeId, String employeeCode,
                                            String employeeName, Integer quantity,
                                            Long advanceAmountTotal, String destinationAccount,
                                            String destinationBankName, Long partnerId) {
        this.employeeId = employeeId;
        this.employeeCode = employeeCode;
        this.employeeName = employeeName;
        this.quantity = quantity;
        this.advanceAmountTotal = advanceAmountTotal;
        this.destinationAccount = destinationAccount;
        this.destinationBankName = destinationBankName;
        this.partnerId = partnerId;
    }
}
