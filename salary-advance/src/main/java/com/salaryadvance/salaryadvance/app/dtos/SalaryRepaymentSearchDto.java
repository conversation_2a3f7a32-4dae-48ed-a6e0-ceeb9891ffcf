package com.salaryadvance.salaryadvance.app.dtos;

import com.salaryadvance.salaryadvance.domain.entities.type.SalaryRepaymentStatus;
import lombok.Data;

@Data
public class SalaryRepaymentSearchDto {
    private Integer month;
    private Integer year;
    private String partnerName;
    private String taxCode;
    private Long repaymentDate;
    private SalaryRepaymentStatus status;
    private Long partnerId;

    public SalaryRepaymentSearchDto(Integer month, Integer year, String partnerName, String taxCode, Long repaymentDate, SalaryRepaymentStatus status, Long partnerId) {
        this.month = month;
        this.year = year;
        this.partnerName = partnerName;
        this.taxCode = taxCode;
        this.repaymentDate = repaymentDate;
        this.status = status;
        this.partnerId = partnerId;
    }
}
