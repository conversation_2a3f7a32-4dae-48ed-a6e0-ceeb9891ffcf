package com.salaryadvance.salaryadvance.domain.repositories;

import com.salaryadvance.salaryadvance.domain.entities.Statistic;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface StatisticRepository extends JpaRepository<Statistic, Long> {
    List<Statistic> findStatisticsByDailyBetween(LocalDate startDate, LocalDate endDate);

    Statistic findStatisticsByPartnerIdAndDaily(Long partnerId, LocalDate localDate);
}
