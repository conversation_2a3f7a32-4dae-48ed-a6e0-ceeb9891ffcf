package com.salaryadvance.salaryadvance.domain.feigns.replies.integration;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class QueryAccountReply {
    @JsonProperty(value = "Data")
    private TransferMoneyReply.Data data;

    private String code;
    private String type;
    private String title;
    private String detail;

    @JsonProperty(value = "errorRes")
    private ErrorReply errorReply;
    @lombok.Data
    public static class Data{
        @JsonProperty(value = "DateTime")
        private String dateTime;

        @JsonProperty(value = "CreditorAccount")
        private CreditorAccount creditorAccount;

        @lombok.Data
        public static class CreditorAccount{
            @JsonProperty(value = "SourceNumber")
            private String sourceNumber;

            @JsonProperty(value = "SourceType")
            private String sourceType;

            @JsonProperty(value = "Amount")
            private Integer amount;
        }

    }
}
