package com.salaryadvance.salaryadvance.domain.feigns.replies.integration;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TokenReply {
    @JsonProperty(value = "access_token")
    private String accessToken;

    @JsonProperty(value = "expires_in")
    private Integer expiresIn;

    @JsonProperty(value = "refresh_expires_in")
    private Integer refreshExpiresIn;

    @JsonProperty(value = "token_type")
    private String tokenType;

    @JsonProperty(value = "not-before-policy")
    private Integer notBeforePolicy;

    @JsonProperty(value = "scope")
    private String scope;

    @JsonProperty(value = "errorRes")
    private ErrorReply errorReply;
}
