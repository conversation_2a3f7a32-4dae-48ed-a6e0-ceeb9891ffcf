package com.salaryadvance.salaryadvance.domain.repositories;

import com.salaryadvance.salaryadvance.domain.entities.SalaryAdvanceTransaction;
import com.salaryadvance.salaryadvance.domain.entities.type.SalaryAdvanceTranStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SalaryAdvanceTransactionRepository extends JpaRepository<SalaryAdvanceTransaction, Long>, JpaSpecificationExecutor<SalaryAdvanceTransaction> {
    SalaryAdvanceTransaction findSalaryAdvanceTransactionsByIdAndEmployeeId(Long id, String employeeId);
    SalaryAdvanceTransaction findSalaryAdvanceTransactionsByAdvanceRequestIdAndEmployeeId(String requestId, String employeeId);
    Page<SalaryAdvanceTransaction> findAllByEmployeeIdAndStatus(String employeeId, SalaryAdvanceTranStatus status, Pageable pageable);
    Page<SalaryAdvanceTransaction> findAllByEmployeeIdAndStatusAndAdvanceAmount(String employeeId,
                                                                                SalaryAdvanceTranStatus status,
                                                                                Integer advanceAmount,
                                                                                Pageable pageable);

    List<SalaryAdvanceTransaction> findAllByIdIn(List<Long> ids);

    @Query(
            value =
                    "select * from salary_advance.transaction t where t.status= ?1 and t.created_at >= ?2 and t.created_at <= ?3",
            nativeQuery = true)
    List<SalaryAdvanceTransaction> statisticTransactionByStatusAndDate(String status, Long startDate, Long endDate);

    @Query(
            value =
                    "select * from salary_advance.transaction t where t.created_at >= ?1 and t.created_at <= ?2",
            nativeQuery = true)
    List<SalaryAdvanceTransaction> statisticTransactionByDate(Long startDate, Long endDate);

//    @Query("SELECT s.partnerId, " +
//            "COUNT(DISTINCT s.employeeCode), " +  // Tổng số nhân viên đã ứng
//            "SUM(s.advanceAmount), " +            // Tổng số tiền ứng
//            "SUM(s.totalFee) " +                   // Tổng phí
//            "FROM SalaryAdvanceTransaction s " +
//            "WHERE s.partnerId IN :partnerIds and s.createdAt >= :startDate and s.createdAt <= :endDate " +
//            "GROUP BY s.partnerId")
//    List<Object[]> getTransactionSummaryByPartner(@Param("partnerIds") List<Long> partnerIds, @Param("startDate") Long startDate, @Param("endDate") Long endDate);

    @Query(value = """
    WITH transaction_fee AS (
        SELECT
            s.id AS transaction_id,
            s.partner_id,
            SUM(CAST(fee->>'fixedFee' AS NUMERIC)) AS fixed_fee,
            SUM(CAST(fee->>'percentFee' AS NUMERIC)) AS percent_fee
        FROM salary_advance.transaction s,
             LATERAL jsonb_array_elements(s.trans_type_info->'feeInfo') AS fee
        WHERE s.partner_id IN (:partnerIds)
          AND s.created_at >= :startDate
          AND s.created_at <= :endDate
        GROUP BY s.id, s.partner_id
    ),
    fee_data AS (
        SELECT
            partner_id,
            SUM(fixed_fee) AS fixed_fee,
            SUM(percent_fee) AS percent_fee
        FROM transaction_fee
        GROUP BY partner_id
    )
    SELECT
        s.partner_id,
        COUNT(DISTINCT s.employee_code) AS total_employees,
        SUM(s.advance_amount) AS total_advance,
        SUM(s.total_fee) AS total_fee,
        COALESCE(MAX(f.fixed_fee), 0) AS total_fixed_fee,
        COALESCE(MAX(f.percent_fee), 0) AS total_percent_fee
    FROM salary_advance.transaction s
    LEFT JOIN fee_data f ON s.partner_id = f.partner_id
    WHERE s.partner_id IN (:partnerIds)
      AND s.created_at >= :startDate
      AND s.created_at <= :endDate
    GROUP BY s.partner_id
    """, nativeQuery = true)
    List<Object[]> getTransactionSummaryByPartner(@Param("partnerIds") List<Long> partnerIds,
                                       @Param("startDate") Long startDate,
                                       @Param("endDate") Long endDate);

}
