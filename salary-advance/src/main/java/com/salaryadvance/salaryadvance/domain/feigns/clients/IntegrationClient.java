package com.salaryadvance.salaryadvance.domain.feigns.clients;

import com.salaryadvance.salaryadvance.domain.feigns.replies.integration.*;
import com.salaryadvance.commonlibrary.rest.Response;
import com.salaryadvance.salaryadvance.domain.feigns.replies.integration.*;
import com.salaryadvance.salaryadvance.domain.feigns.requests.integration.TransferInquiryReq;
import com.salaryadvance.salaryadvance.domain.feigns.requests.integration.TransferMoneyReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(value = "integration-client", url = "${integration.service.url}")
public interface IntegrationClient {

    //lay token
    @PostMapping("api/v1/payment/token")
    Response<TokenReply> getTokenFromBank(@RequestHeader String traceId);

    //query tai khoan nguoi nhan
    @PostMapping("api/v1/payment/transfer/inquiry")
    Response<TransferInquiryReply> transferInquiry(@RequestHeader String token,
                                                   @RequestHeader(value = "x-idempotency-key") String key,
                                                   @RequestBody TransferInquiryReq transferInquiryReq);

    //chuyen tien
    @PostMapping("api/v1/payment/transfer/deposit")
    Response<TransferMoneyReply> transferMoney(@RequestHeader String token,
                                               @RequestHeader(value = "x-idempotency-key") String key,
                                               @RequestBody TransferMoneyReq req);

    //query tai khoan gui
    @GetMapping("api/v1/payment/info/account")
    Response<QueryAccountReply> infoAccount(@RequestHeader String token, @RequestHeader String traceId);

    @GetMapping("api/v1/payment/transaction/recheck")
    Response<RecheckReply> checkTransaction(@RequestHeader String token,
                                            @RequestParam String transactionId,
                                            @RequestHeader String traceId);
}
