package com.salaryadvance.salaryadvance.domain.entities;

import com.salaryadvance.salaryadvance.domain.data.TransInfo;
import com.salaryadvance.salaryadvance.domain.data.TransTypeInfo;
import com.salaryadvance.salaryadvance.domain.entities.type.BankType;
import com.salaryadvance.salaryadvance.domain.entities.type.SalaryAdvanceTranStatus;
import com.salaryadvance.salaryadvance.domain.feigns.replies.integration.TransferMoneyReply;
import com.salaryadvance.commonlibrary.persistence.AuditableEntity;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Type;
import org.hibernate.envers.Audited;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@Table(name = "transaction", schema = "salary_advance")
@Data
@Entity
@Audited
@EntityListeners(AuditingEntityListener.class)
public class SalaryAdvanceTransaction extends AuditableEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "employee_id")
    private String employeeId;

    @Column(name = "employee_code")
    private String employeeCode;

    @Column(name = "employee_name")
    private String employeeName;

    @Column(name = "source_account")
    private String sourceAccount; // tk chuyen tien

    @Column(name = "source_bank_name")
    private String sourceBankName; // ten ngan hang chuyen tien

    @Column(name = "destination_account")
    private String destinationAccount; // tk nhan tien

    @Column(name = "destination_bank_name")
    private String destinationBankName; // ten ngan hang nhan tien

    @Column(name = "bank_type_deposit")
    @Enumerated(EnumType.STRING)
    private BankType bankTypeDeposit; // loai ngan hang chuyen tien

    @Column(name = "advance_amount")
    private Integer advanceAmount; // so tien ung

    @Column(name = "total_fee")
    private Integer totalFee; // tong so phi phai tra

    @Column(name = "actual_received_amount")
    private Integer actualReceivedAmount; // so tien thuc te nhan duoc = advanceAmount - totalFee

    @Column(name = "advance_request_id")
    private String advanceRequestId; // ma yeu cau ung tien

    @Column(name = "ref_id")
    private String refId;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private SalaryAdvanceTranStatus status;

    @Column(name = "num_process")
    private Integer numProcess;

    @Column(name = "partner_id")
    private Long partnerId;

    @Column(name = "description")
    private String description;

    @Column(name = "error_code")
    private String errorCode;

    @Type(JsonBinaryType.class)
    @Column(name = "trans_info", columnDefinition = "jsonb")
    private TransInfo transInfo;

    @Column(name = "message_id")
    private String messageId;

    @Type(JsonBinaryType.class)
    @Column(name = "trans_type_info", columnDefinition = "jsonb")
    private TransTypeInfo transTypeInfo;

//    @Column(name = "total_fee")
//    private Integer totalFixedFee;
//
//    @Column(name = "total_fee")
//    private Integer totalPercentFee;

    public void createFrom(
                           String employeeId,
                           String employeeName,
                           String employeeCode,
                           String destinationAccount,
                           String destinationBankName,
                           Integer advanceAmount,
                           String requestId,
                           Integer totalFee,
                           Integer actualReceivedAmount,
                           Long partnerId,
                           TransTypeInfo transTypeInfo,
                           String refId,
                           String sourceAccount,
                           String sourceBankName,
                           BankType bankTypeDeposit){
        setEmployeeId(employeeId);
        setEmployeeName(employeeName);
        setEmployeeCode(employeeCode);
//        setSourceAccount("************");//todo check lai cho nay
//        setSourceBankName("Ngân hàng TMCP Đông Á");//todo check lai cho nay
        setSourceAccount(sourceAccount);
        setSourceBankName(sourceBankName);
        setBankTypeDeposit(bankTypeDeposit);
        setDestinationAccount(destinationAccount);
        setDestinationBankName(destinationBankName);
        setAdvanceAmount(advanceAmount);
        setAdvanceRequestId(requestId);
        setTotalFee(totalFee);
        setActualReceivedAmount(actualReceivedAmount);
        setNumProcess(1);
        setPartnerId(partnerId);
        setStatus(SalaryAdvanceTranStatus.PROCESSING);
        setDescription(SalaryAdvanceTranStatus.PROCESSING.getDescription());
        setTransTypeInfo(transTypeInfo);
        setRefId(refId);
    }
}
