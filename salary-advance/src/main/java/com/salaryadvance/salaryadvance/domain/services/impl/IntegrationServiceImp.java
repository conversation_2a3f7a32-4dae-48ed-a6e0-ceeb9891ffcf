package com.salaryadvance.salaryadvance.domain.services.impl;

import com.salaryadvance.salaryadvance.domain.feigns.clients.IntegrationClient;
import com.salaryadvance.salaryadvance.domain.feigns.replies.integration.*;
import com.salaryadvance.commonlibrary.rest.Response;
import com.salaryadvance.salaryadvance.domain.feigns.replies.integration.*;
import com.salaryadvance.salaryadvance.domain.feigns.requests.integration.TransferInquiryReq;
import com.salaryadvance.salaryadvance.domain.feigns.requests.integration.TransferMoneyReq;
import com.salaryadvance.salaryadvance.domain.services.IntegrationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
public class IntegrationServiceImp implements IntegrationService {
    @Autowired
    private IntegrationClient integrationClient;

    @Override
    public Response<TokenReply> getToken(String tokenInternal) {
        return integrationClient.getTokenFromBank(UUID.randomUUID().toString());
    }

    @Override
    public Response<ListBankReply> getListBank(String tokenInternal) {
        return null;
    }

    @Override
    public Response<TransferInquiryReply> transferInquiry(String tokenInternal, String key, TransferInquiryReq transferInquiryReq) {
        return integrationClient.transferInquiry(tokenInternal, key, transferInquiryReq);
    }

    @Override
    public Response<TransferMoneyReply> transferMoney(String tokenInternal, String key, TransferMoneyReq req) {
        return integrationClient.transferMoney(tokenInternal, key, req);
    }

    @Override
    public Response<QueryAccountReply> queryAccount(String tokenInternal) {
        return integrationClient.infoAccount(tokenInternal, UUID.randomUUID().toString());
    }

    @Override
    public Response<RecheckReply> checkTransaction(String tokenInternal, String transactionId) {
        return integrationClient.checkTransaction(tokenInternal, transactionId, UUID.randomUUID().toString());
    }
}
