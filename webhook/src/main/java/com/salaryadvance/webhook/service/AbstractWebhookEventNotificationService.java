package com.salaryadvance.webhook.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.salaryadvance.webhook.repository.WebhookEventNotificationRepository;
import com.salaryadvance.webhook.model.WebhookEvent;
import com.salaryadvance.webhook.model.WebhookEventNotification;
import com.salaryadvance.webhook.model.dto.WebhookEventNotificationDto;
import com.salaryadvance.webhook.model.enums.NotificationStatus;

import java.time.ZonedDateTime;

abstract class AbstractWebhookEventNotificationService {

    protected abstract WebhookEventNotificationRepository getWebhookEventNotificationRepository();

    protected Long persistNotification(Long webhookEventId, JsonNode payload) {
        WebhookEventNotification notification = new WebhookEventNotification();
        notification.setWebhookEventId(webhookEventId);
        notification.setPayload(payload.toString());
        notification.setNotificationStatus(NotificationStatus.NOTIFYING);
        notification.setCreatedOn(ZonedDateTime.now());
        WebhookEventNotification persistedNotification = getWebhookEventNotificationRepository().save(notification);
        return persistedNotification.getId();
    }

    protected WebhookEventNotificationDto createNotificationDto(WebhookEvent webhookEvent, JsonNode payload,
                                                                Long notificationId) {
        String url = webhookEvent.getWebhook().getPayloadUrl();
        String secret = webhookEvent.getWebhook().getSecret();

        return WebhookEventNotificationDto.builder()
            .secret(secret)
            .payload(payload)
            .url(url)
            .notificationId(notificationId)
            .build();
    }
}
