input {
  beats {
    ssl => false
    port => 5044
  }
}

filter {
  grok {
    match => { "message" => [
      # Single-line log (không có MDC, không exception)
      "%{DATESTAMP:timestamp} \[%{DATA:thread}\] %{LOGLEVEL:loglevel}\s+(?<logger>[^\s]+)\.%{WORD:method} - %{GREEDYDATA:logmessage}",
      # Single-line log có MDC
      "%{DATESTAMP:timestamp} \[%{DATA:thread}\] %{LOGLEVEL:loglevel}\s+(?<logger>[^\s]+)\.%{WORD:method} - %{DATA:logmessage} - %{DATA:mdc}",
      # Multi-line log có MDC và exception (dòng cuối cùng là stacktrace)
      "%{DATESTAMP:timestamp} \[%{DATA:thread}\] %{LOGLEVEL:loglevel}\s+(?<logger>[^\s]+)\.%{WORD:method} - %{DATA:logmessage} - %{DATA:mdc}\n(?:(?<exception>(.|\r|\n|\t)*))?"
    ]}
  }

  if [exception] {
    mutate {
      add_field => { "cause" => "%{exception}" }
    }
  }

  if [mdc] {
    mutate {
      add_field => { "mdc_flag" => "true" }
    }
    grok {
      match => { "mdc" => "(?<before_serviceHeader>.*?)\s*serviceHeader=\{(?<httpHeader>.*?)\},?\s*(?<after_serviceHeader>.*)" }
    }
    grok {
      match => { "mdc" => 'httpMethod=%{WORD:http_method}, clientMessageId=%{UUID:client_message_id}' }
    }
    if [httpHeader] {
      mutate {
        add_field => { "serviceHeader" => "{%{httpHeader}}" }
      }
      json {
        source => "serviceHeader"
        target => "serviceHeader"
      }

      # Add httpMethod and clientMessageId extraction
      if [serviceHeader][httpMethod] {
          mutate {
              add_field => { "http_method" => "%{[serviceHeader][httpMethod]}" }
          }
      }
      if [serviceHeader][clientMessageId] {
          mutate {
              add_field => { "client_message_id" => "%{[serviceHeader][clientMessageId]}" }
          }
      }
    }
    if [before_serviceHeader] {
      kv {
        source => "before_serviceHeader"
        field_split => ", "
        value_split => "="
      }
    }
    if [after_serviceHeader] {
      kv {
        source => "after_serviceHeader"
        field_split => ", "
        value_split => "="
      }
    }
    mutate {
      remove_field => ["mdc", "after_serviceHeader", "before_serviceHeader", "httpHeader", "exception"]
    }
  }
}

output {
  elasticsearch {
    hosts => ["http://elasticsearch:9200"]
    index => "logs-%{[fields][application_name]}"
  }
  stdout {
    codec => rubydebug
  }
}