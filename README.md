# TAM UNG LUONG (Salary Advance System)

## Overview
TAM UNG LUONG is a salary advance management system built with Spring Boot. The system allows employees to request salary advances, manages employee attendance, and handles salary calculations based on attendance records.

## Project Structure
```
tamungluong/
├── portal/                      # Main application module
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/
│   │   │   │   └── org/pronexus/portal/
│   │   │   │       ├── app/                    # Application layer
│   │   │   │       │   ├── controllers/        # REST controllers
│   │   │   │       │   ├── dtos/              # Data Transfer Objects
│   │   │   │       │   └── response/          # Response models
│   │   │   │       ├── domain/                # Domain layer
│   │   │   │       │   ├── entities/          # Domain entities
│   │   │   │       │   ├── mappers/           # Object mappers
│   │   │   │       │   ├── repositories/      # Data access layer
│   │   │   │       │   ├── services/          # Business logic layer
│   │   │   │       │   │   ├── core/         # Service interfaces
│   │   │   │       │   │   ├── helper/       # Helper services
│   │   │   │       │   │   └── impl/         # Service implementations
│   │   │   │       │   └── utils/            # Utility classes
│   │   │   │       └── config/               # Configuration classes
│   │   │   └── resources/                    # Application resources
│   │   └── test/                             # Test files
│   └── pom.xml                               # Maven configuration
└── user/                                     # User service module
    ├── src/
    │   └── main/
    │       └── java/
    │           └── org/pronexus/user/
    └── pom.xml
```

## Architecture

### Layered Architecture
The application follows a layered architecture pattern:

1. **Presentation Layer** (`app/controllers/`)
   - Handles HTTP requests
   - Input validation
   - Response formatting

2. **Application Layer** (`app/dtos/`, `app/response/`)
   - Data Transfer Objects (DTOs)
   - Request/Response models
   - Input validation

3. **Domain Layer** (`domain/`)
   - Business entities
   - Business logic
   - Domain services

4. **Data Access Layer** (`domain/repositories/`)
   - Database access
   - Entity persistence
   - Query specifications

### Key Features

1. **Employee Management**
   - CRUD operations for employees
   - Employee search and filtering
   - Employee import/export functionality

2. **Salary Advance**
   - Salary advance limit calculation
   - Credit amount management
   - Fee calculation and management

3. **Attendance Management**
   - Daily attendance tracking
   - Timesheet management
   - Attendance history

4. **Reporting**
   - Salary reports
   - Timesheet reports
   - Excel export functionality

### Technology Stack

- **Framework**: Spring Boot
- **Database**: JPA/Hibernate
- **Security**: Spring Security
- **API Documentation**: OpenAPI/Swagger
- **Excel Processing**: Apache POI
- **Build Tool**: Maven

## Getting Started

### Prerequisites
- Java 17 or higher
- Maven 3.6 or higher
- MySQL/PostgreSQL database

### Building the Project
```bash
mvn clean install
```

### Running the Application
```bash
mvn spring-boot:run
```

## API Documentation
API documentation is available at `/swagger-ui.html` when the application is running.

## Contributing
1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a new Pull Request

## License
This project is licensed under the MIT License - see the LICENSE file for details.

#fix jenkins nếu tự build bằng root lỗi ko có quyền xoá thì cần chạy lệnh này

`sudo find /var/lib/jenkins/workspace -type d -name "target" -exec chown -R jenkins:jenkins {} +`

#cerbot
https://chatgpt.com/share/678e5ba0-7e84-800e-b463-7b796359a7a3


`docker-compose run --rm --entrypoint "\
certbot certonly --webroot \
--webroot-path=/var/www/certbot \
--email <EMAIL> \
--agree-tos \
--no-eff-email \
-d api.pronexus.vn \
-d admin.pronexus.vn \
-d portal.pronexus.vn \
-d identity.pronexus.vn" certbot
`

docker-compose run --rm --entrypoint "\
certbot certonly --webroot \
--webroot-path=/var/www/certbot \
--email <EMAIL> \
--agree-tos \
--no-eff-email \
-d api.pronexus.vn \
-d admin.pronexus.vn \
-d identity.pronexus.vn" certbot