# Hướng dẫn Triển khai CRUD trong Dự án PVCombank

Tài liệu này cung cấp hướng dẫn chi tiết về cách triển khai các hoạt động CRUD (Create, Read, Update, Delete) trong dự án P<PERSON>ombank, tuân theo các quy ước và mẫu thiết kế đã được thiết lập.

## Cấu trúc Tổng quan

Để triển khai một tính năng CRUD hoàn chỉnh, bạn cần tạo các thành phần sau:

1. **Entity**: Đ<PERSON>i diện cho bảng trong cơ sở dữ liệu
2. **Repository**: Giao diện cung cấp các phương thức truy cập dữ liệu
3. **DTO**: Các đối tượng truyền dữ liệu giữa các lớp
4. **Mapper**: Chuyển đổi giữa Entity và DTO
5. **Service**: <PERSON><PERSON> lý logic nghiệp vụ
6. **Controller**: Endpoint API cho client

## Chi tiết từng Thành phần

### 1. Entity

```java
package org.pronexus.portal.domain.entities;

import com.salaryadvance.commonlibrary.persistence.AuditableEntity;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "your_entity")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class YourEntity extends AuditableEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String name;
    
    // Các trường khác...
}
```

**Chú ý**:
- Luôn kế thừa `AuditableEntity` để tự động quản lý các trường audit
- Thêm `@EqualsAndHashCode(callSuper = false)` khi kế thừa
- Không tự định nghĩa các trường audit (createdAt, updatedAt, createdBy, updatedBy)

### 2. Repository

```java
package org.pronexus.portal.domain.repositories;

import org.pronexus.portal.domain.entities.YourEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface YourEntityRepository extends JpaRepository<YourEntity, Long> {
    
    // Các phương thức tìm kiếm tùy chỉnh
    Page<YourEntity> findByNameContainingIgnoreCase(String name, Pageable pageable);
    
    // Các phương thức khác...
}
```

### 3. DTO

**Model Response**:
```java
package org.pronexus.portal.app.dtos.yourentity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class YourEntityModelRes {
    private Long id;
    private String name;
    // Các trường khác...
    
    // Các trường audit từ AuditableEntity
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String createdBy;
    private String updatedBy;
}
```

**Create Command**:
```java
package org.pronexus.portal.app.dtos.yourentity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateYourEntityCommandDto {
    private String name;
    // Các trường khác...
}
```

**Update Command**:
```java
package org.pronexus.portal.app.dtos.yourentity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpdateYourEntityCommandDto {
    private String name;
    // Các trường khác...
}
```

**Criteria DTO**:
```java
package org.pronexus.portal.app.dtos.yourentity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class YourEntityCriteriaDto {
    private String name;
    // Các tiêu chí tìm kiếm khác...
}
```

### 4. Mapper

```java
package org.pronexus.portal.domain.mappers;

import com.salaryadvance.commonlibrary.mapper.AuditMapper;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.pronexus.portal.app.dtos.yourentity.CreateYourEntityCommandDto;
import org.pronexus.portal.app.dtos.yourentity.YourEntityModelRes;
import org.pronexus.portal.app.dtos.yourentity.UpdateYourEntityCommandDto;
import org.pronexus.portal.domain.entities.YourEntity;

@Mapper(componentModel = "spring", uses = { AuditMapper.class })
public interface YourEntityMapper {

    YourEntityModelRes toYourEntityModelRes(YourEntity yourEntity);

    YourEntity toYourEntity(CreateYourEntityCommandDto createCommand);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateYourEntityFromDto(@MappingTarget YourEntity yourEntity, UpdateYourEntityCommandDto updateCommand);
}
```

### 5. Service Interface

```java
package org.pronexus.portal.domain.services.core;

import com.salaryadvance.commonlibrary.rest.CommandResponse;
import org.pronexus.portal.app.dtos.yourentity.CreateYourEntityCommandDto;
import org.pronexus.portal.app.dtos.yourentity.YourEntityCriteriaDto;
import org.pronexus.portal.app.dtos.yourentity.YourEntityModelRes;
import org.pronexus.portal.app.dtos.yourentity.UpdateYourEntityCommandDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface YourEntityService {
    
    YourEntityModelRes find(Long id);
    
    Page<YourEntityModelRes> getYourEntities(YourEntityCriteriaDto criteria, Pageable pageable);
    
    CommandResponse<YourEntityModelRes> create(CreateYourEntityCommandDto command);
    
    CommandResponse<YourEntityModelRes> update(Long id, UpdateYourEntityCommandDto command);
    
    CommandResponse<Void> delete(Long id);
}
```

### 6. Service Implementation

```java
package org.pronexus.portal.domain.services.impl;

import com.salaryadvance.commonlibrary.constants.ApiMessage;
import com.salaryadvance.commonlibrary.exception.NotFoundException;
import com.salaryadvance.commonlibrary.rest.CommandResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.pronexus.portal.app.dtos.yourentity.CreateYourEntityCommandDto;
import org.pronexus.portal.app.dtos.yourentity.YourEntityCriteriaDto;
import org.pronexus.portal.app.dtos.yourentity.YourEntityModelRes;
import org.pronexus.portal.app.dtos.yourentity.UpdateYourEntityCommandDto;
import org.pronexus.portal.domain.entities.YourEntity;
import org.pronexus.portal.domain.mappers.YourEntityMapper;
import org.pronexus.portal.domain.repositories.YourEntityRepository;
import org.pronexus.portal.domain.services.core.YourEntityService;
import org.pronexus.portal.domain.utils.Constants;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class YourEntityServiceImpl extends BaseService implements YourEntityService {

    private final YourEntityRepository yourEntityRepository;
    private final YourEntityMapper yourEntityMapper;

    @Override
    public YourEntityModelRes find(Long id) {
        YourEntity yourEntity = yourEntityRepository.findById(id)
                .orElseThrow(() -> new NotFoundException(String.format(Constants.NOT_FOUND_MESSAGE, "YourEntity", "id", id)));
        return yourEntityMapper.toYourEntityModelRes(yourEntity);
    }

    @Override
    public Page<YourEntityModelRes> getYourEntities(YourEntityCriteriaDto criteria, Pageable pageable) {
        // Xử lý tìm kiếm dựa trên tiêu chí
        if (criteria.getName() != null) {
            return yourEntityRepository.findByNameContainingIgnoreCase(criteria.getName(), pageable)
                    .map(yourEntityMapper::toYourEntityModelRes);
        }
        
        // Trả về tất cả nếu không có tiêu chí
        return yourEntityRepository.findAll(pageable)
                .map(yourEntityMapper::toYourEntityModelRes);
    }

    @Override
    @Transactional
    public CommandResponse<YourEntityModelRes> create(CreateYourEntityCommandDto command) {
        YourEntity yourEntity = yourEntityMapper.toYourEntity(command);
        YourEntity savedEntity = yourEntityRepository.save(yourEntity);
        return CommandResponse.success(yourEntityMapper.toYourEntityModelRes(savedEntity), 
                ApiMessage.created("YourEntity"));
    }

    @Override
    @Transactional
    public CommandResponse<YourEntityModelRes> update(Long id, UpdateYourEntityCommandDto command) {
        YourEntity yourEntity = yourEntityRepository.findById(id)
                .orElseThrow(() -> new NotFoundException(String.format(Constants.NOT_FOUND_MESSAGE, "YourEntity", "id", id)));
        
        yourEntityMapper.updateYourEntityFromDto(yourEntity, command);
        YourEntity savedEntity = yourEntityRepository.save(yourEntity);
        
        return CommandResponse.success(yourEntityMapper.toYourEntityModelRes(savedEntity),
                ApiMessage.updated("YourEntity"));
    }

    @Override
    @Transactional
    public CommandResponse<Void> delete(Long id) {
        YourEntity yourEntity = yourEntityRepository.findById(id)
                .orElseThrow(() -> new NotFoundException(String.format(Constants.NOT_FOUND_MESSAGE, "YourEntity", "id", id)));
        
        yourEntityRepository.delete(yourEntity);
        return CommandResponse.success(null, ApiMessage.deleted("YourEntity"));
    }
}
```

### 7. Controller

```java
package org.pronexus.portal.app.controllers;

import com.salaryadvance.commonlibrary.constants.HttpConstant;
import com.salaryadvance.commonlibrary.constants.HttpKey;
import com.salaryadvance.commonlibrary.rest.CommandResponse;
import com.salaryadvance.commonlibrary.rest.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.pronexus.portal.app.dtos.yourentity.CreateYourEntityCommandDto;
import org.pronexus.portal.app.dtos.yourentity.YourEntityCriteriaDto;
import org.pronexus.portal.app.dtos.yourentity.YourEntityModelRes;
import org.pronexus.portal.app.dtos.yourentity.UpdateYourEntityCommandDto;
import org.pronexus.portal.domain.services.core.YourEntityService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/v1/your-entities")
@RequiredArgsConstructor
public class YourEntityController {

    private final YourEntityService yourEntityService;

    @GetMapping("/{id}")
    public ResponseEntity<Response<YourEntityModelRes>> findYourEntity(@PathVariable(value = "id") Long id) {
        YourEntityModelRes body = yourEntityService.find(id);
        return Response.success(body);
    }

    @GetMapping()
    public ResponseEntity<Response<Page<YourEntityModelRes>>> getYourEntities(
            @RequestParam(value = "name", required = false) String name,
            // Các tham số tìm kiếm khác...
            @RequestParam(value = HttpKey.PAGE, defaultValue = HttpConstant.DEFAULT_PAGE_NUMBER) Integer page,
            @RequestParam(value = HttpKey.SIZE, defaultValue = HttpConstant.DEFAULT_PAGE_SIZE) Integer size) {
        YourEntityCriteriaDto criteria = YourEntityCriteriaDto.builder()
                .name(name)
                // Thiết lập các tiêu chí khác...
                .build();
        Pageable pageable = PageRequest.of(page, size);
        Page<YourEntityModelRes> body = yourEntityService.getYourEntities(criteria, pageable);
        return Response.success(body);
    }

    @PostMapping()
    public ResponseEntity<Response<CommandResponse<YourEntityModelRes>>> createYourEntity(
            @RequestBody CreateYourEntityCommandDto command) {
        CommandResponse<YourEntityModelRes> body = yourEntityService.create(command);
        return Response.success(body);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Response<CommandResponse<YourEntityModelRes>>> updateYourEntity(
            @PathVariable(value = "id") Long id,
            @RequestBody UpdateYourEntityCommandDto command) {
        CommandResponse<YourEntityModelRes> body = yourEntityService.update(id, command);
        return Response.success(body);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Response<CommandResponse<Void>>> deleteYourEntity(@PathVariable(value = "id") Long id) {
        CommandResponse<Void> body = yourEntityService.delete(id);
        return Response.success(body);
    }
}
```

## Các Quy Tắc và Lưu Ý Quan Trọng

1. **Entity**:
   - Luôn kế thừa AuditableEntity để quản lý tự động các trường audit
   - Thêm annotation @EqualsAndHashCode(callSuper = false) khi kế thừa
   - Không tự định nghĩa các trường audit

2. **Exception Handling**:
   - Sử dụng NotFoundException từ package com.salaryadvance.commonlibrary.exception
   - Sử dụng format Constants.NOT_FOUND_MESSAGE cho thông báo lỗi

3. **CommandResponse**:
   - Sử dụng phương thức tĩnh CommandResponse.success() thay vì builder pattern
   - Sử dụng ApiMessage.created(), ApiMessage.updated(), ApiMessage.deleted() cho thông báo

4. **Transactional**:
   - Đánh dấu @Transactional cho các phương thức thực hiện thay đổi dữ liệu

5. **Mapper**:
   - Sử dụng @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE) 
     để chỉ cập nhật các trường không null trong quá trình update

## Sử dụng Kết quả Cuối cùng

Sau khi triển khai tất cả các thành phần trên, bạn sẽ có các API endpoints sau:

```
GET    /api/v1/your-entities/{id}  - Lấy thông tin theo ID
GET    /api/v1/your-entities       - Lấy danh sách với các filter 
POST   /api/v1/your-entities       - Tạo mới
PUT    /api/v1/your-entities/{id}  - Cập nhật
DELETE /api/v1/your-entities/{id}  - Xóa
```

Các endpoints này sẽ cung cấp đầy đủ các chức năng CRUD cho entity của bạn, tuân theo các quy ước và mẫu thiết kế của dự án PVCombank.
