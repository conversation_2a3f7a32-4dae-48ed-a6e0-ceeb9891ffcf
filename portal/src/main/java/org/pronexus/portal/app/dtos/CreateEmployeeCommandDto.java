package org.pronexus.portal.app.dtos;

import lombok.Data;
import org.pronexus.portal.domain.entities.Address;
import org.pronexus.portal.domain.entities.BankInfo;
import org.pronexus.portal.domain.entities.type.ContractType;
import org.pronexus.portal.domain.entities.type.EmployeeStatus;

import java.time.LocalDate;
import java.util.List;

@Data
public class CreateEmployeeCommandDto {
    private String userId;
    private String avatar;
    private String code;
    private String name;
    private Integer gender;
    private String dateOfBirth;
    private ContractType contractType;
    private List<Address> address;
    private Integer salary;
    private Long partnerId;
    private Integer departmentId;
    private String teamId;
    private EmployeeStatus status;
    private String identifierNo;
    private String issueDate;
    private String issuePlace;
    private String phoneNumber;
    private String email;
    private boolean canEditAccount;
    private List<BankInfo> bankAccounts;
    private Integer creditLimit;
    private String position;
    private String note;
}
