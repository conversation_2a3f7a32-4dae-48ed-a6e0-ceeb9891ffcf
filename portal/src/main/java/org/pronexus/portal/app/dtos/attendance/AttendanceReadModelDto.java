package org.pronexus.portal.app.dtos.attendance;

import lombok.Data;
import org.pronexus.portal.domain.entities.attendance.AttendanceState;

@Data
public class AttendanceReadModelDto {
    private Long id;
    private String code;
    private Long employeeId;
    private Long partnerId;
    private Long departmentId;
    private Long teamId;

    @Data
    static class AttendanceDetails {
        private Integer day;
        private Integer month;
        private Integer year;
        private String result;
        private boolean isUnderWork;
        private Integer underWorkTime;
        private AttendanceState state;
    }
}
