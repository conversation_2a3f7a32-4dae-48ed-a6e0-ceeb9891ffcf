package org.pronexus.portal.domain.repositories;

import org.pronexus.portal.domain.entities.Ward;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface WardRepository extends JpaRepository<Ward, Long>, JpaSpecificationExecutor<Ward> {
    Page<Ward> findAllByNameContainingAndCodeContaining(String name, String code, Pageable pageable);
}