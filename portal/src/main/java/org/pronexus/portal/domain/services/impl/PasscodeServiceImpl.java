package org.pronexus.portal.domain.services.impl;

import com.salaryadvance.commonlibrary.exception.BadRequestException;
import com.salaryadvance.commonlibrary.exception.ForbiddenException;
import com.salaryadvance.commonlibrary.exception.base.DataValidationException;
import com.salaryadvance.commonlibrary.rest.CommandResponse;
import com.salaryadvance.commonlibrary.utils.TokenUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.pronexus.portal.app.dtos.employee.ResetPasscodeCommandDto;
import org.pronexus.portal.app.dtos.employee.SetupPasscodeCommandDto;
import org.pronexus.portal.app.dtos.employee.VerifyPasscodeCommandDto;
import org.pronexus.portal.domain.entities.Employee;
import org.pronexus.portal.domain.repositories.EmployeeRepository;
import org.pronexus.portal.domain.services.core.PasscodeService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * Implementation of the PasscodeService for managing employee passcodes
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PasscodeServiceImpl implements PasscodeService {

    private final EmployeeRepository employeeRepository;
    private final PasswordEncoder passwordEncoder;

    // Số lần nhập sai tối đa trước khi khóa
    private static final int MAX_FAILED_ATTEMPTS = 3;
    // Thời gian hết hạn passcode (30 ngày)
    private static final int PASSCODE_EXPIRY_DAYS = 30;

    @Override
    @Transactional
    public CommandResponse<Void> setupPasscode(SetupPasscodeCommandDto command) {
        String userId = TokenUtils.getUserId();
        Employee employee = employeeRepository.findByUserId(userId)
                .orElseThrow(() -> new BadRequestException("Tài khoản của bạn chưa chọn nơi làm việc. Bạn cần xác thực nơi làm việc trước khi tạo pass code"));

        validatePasscode(command.getPasscode(), command.getConfirmPasscode());

        // If user already has passcode enabled, require old passcode verification
        if (Boolean.TRUE.equals(employee.getPasscodeEnabled())) {
            if (command.getOldPasscode() == null) {
                throw new BadRequestException("Old passcode is required when changing existing passcode");
            }

            if (!passwordEncoder.matches(command.getOldPasscode(), employee.getPasscode())) {
                throw new BadRequestException("Old passcode is incorrect");
            }
        }

        employee.setPasscode(passwordEncoder.encode(command.getPasscode()));
        employee.setPasscodeExpiryDate(LocalDateTime.now().plusDays(PASSCODE_EXPIRY_DAYS));
        employee.setPasscodeFailedAttempts(0);
        employee.setPasscodeLastResetDate(LocalDateTime.now());
        employee.setPasscodeEnabled(true);

        employeeRepository.save(employee);

        log.info("Passcode setup successfully for employee: {}", employee.getId());
        return CommandResponse.success(null, "Passcode setup successfully");
    }

    @Override
    @Transactional
    public CommandResponse<Void> verifyPasscode(VerifyPasscodeCommandDto command) {
        String userId = TokenUtils.getUserId();
        Employee employee = employeeRepository.findByUserId(userId)
                .orElseThrow(() -> new BadRequestException("Employee not found"));
        // Todo: mac dinh luon bat passcode
        if (!Boolean.TRUE.equals(employee.getPasscodeEnabled())) {
            throw new BadRequestException("Bạn cần bật passcode trước khi xác thực");
        }
        //
        // if (employee.getPasscodeExpiryDate().isBefore(LocalDateTime.now())) {
        // throw new BadRequestException("Passcode has expired. Please reset your
        // passcode");
        // }
        //
        // if (employee.getPasscodeFailedAttempts() >= MAX_FAILED_ATTEMPTS) {
        // throw new ForbiddenException("Passcode is locked due to too many failed
        // attempts");
        // }

        if (!passwordEncoder.matches(command.getPasscode(), employee.getPasscode())) {
            employee.setPasscodeFailedAttempts(employee.getPasscodeFailedAttempts() + 1);
            employeeRepository.save(employee);

            if (employee.getPasscodeFailedAttempts() >= MAX_FAILED_ATTEMPTS) {
                log.warn("Passcode locked for employee: {}", employee.getId());
                throw new ForbiddenException("Passcode khóa do quá nhiều lần nhập sai");
            }

            throw new BadRequestException("Sai passcode. Vui lòng thử lại");
        }

        // Xác thực thành công, reset số lần thất bại
        employee.setPasscodeFailedAttempts(0);
        employeeRepository.save(employee);

        // Xác nhận giao dịch nếu có transactionId
        if (command.getTransactionId() != null) {
            // TODO: Integrate with transaction confirmation service
            // transManagementService.confirmTransaction(command.getTransactionId());
            log.info("Transaction confirmed with ID: {}", command.getTransactionId());
        }

        log.info("Passcode verified successfully for employee: {}", employee.getId());
        return CommandResponse.success(null, "Passcode verified successfully");
    }

    @Override
    @Transactional
    public CommandResponse<Void> resetPasscode(ResetPasscodeCommandDto command) {
        String userId = TokenUtils.getUserId();
        Employee employee = employeeRepository.findByUserId(userId)
                .orElseThrow(() -> new BadRequestException("Employee not found"));

        if (!Boolean.TRUE.equals(employee.getPasscodeEnabled())) {
            throw new BadRequestException("Passcode is not enabled");
        }

        // Xác thực passcode cũ
        if (!passwordEncoder.matches(command.getOldPasscode(), employee.getPasscode())) {
            throw new DataValidationException("Old passcode is incorrect", null);
        }

        validatePasscode(command.getNewPasscode(), command.getConfirmPasscode());

        employee.setPasscode(passwordEncoder.encode(command.getNewPasscode()));
        employee.setPasscodeExpiryDate(LocalDateTime.now().plusDays(PASSCODE_EXPIRY_DAYS));
        employee.setPasscodeFailedAttempts(0);
        employee.setPasscodeLastResetDate(LocalDateTime.now());

        employeeRepository.save(employee);

        log.info("Passcode reset successfully for employee: {}", employee.getId());
        return CommandResponse.success(null, "Passcode reset successfully");
    }

    @Override
    @Transactional
    public CommandResponse<Void> disablePasscode() {
        String userId = TokenUtils.getUserId();
        Employee employee = employeeRepository.findByUserId(userId)
                .orElseThrow(() -> new BadRequestException("Employee not found"));

        employee.setPasscodeEnabled(false);
        employeeRepository.save(employee);

        log.info("Passcode disabled for employee: {}", employee.getId());
        return CommandResponse.success(null, "Passcode disabled successfully");
    }

    @Override
    public CommandResponse<Boolean> isPasscodeEnabled() {
        String userId = TokenUtils.getUserId();
        Employee employee = employeeRepository.findByUserId(userId)
                .orElseThrow(() -> new BadRequestException("Employee not found"));

        boolean isEnabled = Boolean.TRUE.equals(employee.getPasscodeEnabled());
        return CommandResponse.success(isEnabled, "Passcode status retrieved successfully");
    }

    /**
     * Validate passcode format and matching confirmation
     * 
     * @param passcode        The passcode to validate
     * @param confirmPasscode The confirmation passcode
     * @throws BadRequestException if validation fails
     */
    private void validatePasscode(String passcode, String confirmPasscode) {
        if (passcode == null || passcode.length() < 6) {
            throw new BadRequestException("Passcode must be at least 6 characters");
        }

        if (!passcode.equals(confirmPasscode)) {
            throw new BadRequestException("Passcode and confirm passcode do not match");
        }
    }
}
