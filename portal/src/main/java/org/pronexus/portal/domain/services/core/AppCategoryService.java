package org.pronexus.portal.domain.services.core;

import org.pronexus.portal.app.dtos.AppCategoryCriteriaDto;
import org.pronexus.portal.app.dtos.CreateUpdateAppCategoryDto;
import org.pronexus.portal.app.response.AppCategoryRes;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface AppCategoryService {
    Boolean createAppCategory(CreateUpdateAppCategoryDto dto);

    Boolean updateAppCategory(Integer id, CreateUpdateAppCategoryDto dto);

    AppCategoryRes detailAppCategory(Integer id);

    Boolean deleteAppCategory(Integer id);

    Page<AppCategoryRes> getAppCategory(Pageable pageable);

    Page<AppCategoryRes> getAppCategory(AppCategoryCriteriaDto criteria, Pageable pageable);
}
