package org.pronexus.portal.domain.mappers;

import com.salaryadvance.commonlibrary.mapper.AuditMapper;
import org.mapstruct.*;
import org.pronexus.portal.app.dtos.CreateEmployeeCommandDto;
import org.pronexus.portal.app.dtos.EmployeeImportDataDto;
import org.pronexus.portal.app.dtos.EmployeeModelRes;
import org.pronexus.portal.app.dtos.UpdateEmployeeCommandDto;
import org.pronexus.portal.domain.entities.*;
import org.pronexus.portal.domain.entities.type.ContractType;

import java.util.List;

@Mapper(componentModel = "spring", uses = { AuditMapper.class })
public interface EmployeeMapper {

    EmployeeModelRes toEmployeeModelRes(Employee employee);

    @Mapping(target = "dateOfBirth", ignore = true)
    @Mapping(target = "issueDate", ignore = true)
    Employee toEmployee(CreateEmployeeCommandDto createCommand);

    @Mapping(target = "dateOfBirth", ignore = true)
    @Mapping(target = "issueDate", ignore = true)
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void partialMapping(@MappingTarget Employee employee, UpdateEmployeeCommandDto updateCommand);

    @Mapping(target = "gender", ignore = true)
    @Mapping(target = "contractType", ignore = true)
    Employee toEmployee(EmployeeImportDataDto importData);

    @Named("emptyToNull")
    static Integer emptyToNull(String value) {
        return (value == null || value.trim().isEmpty()) ? null : Integer.valueOf(value);
    }

    default Employee mapToEmployee(EmployeeImportDataDto importData) {
        Employee employee = toEmployee(importData);
        employee.setGender(Gender.getGender(importData.getGender()));
        employee.setContractType(ContractType.of(importData.getContractType()));
        Address address = new Address();
        address.setProvince(importData.getProvince());
        address.setDistrict(importData.getDistrict());
        address.setAddress(importData.getDetail());
        employee.setAddress(List.of(address));
        BankInfo bankInfo = new BankInfo();
        bankInfo.setBankName(importData.getBankName());
        bankInfo.setBankAccountNumber(importData.getBankAccountNumber());
        bankInfo.setOwnerName(importData.getOwnerName());
        employee.setBankAccounts(List.of(bankInfo));
        return employee;
    }

}
