package org.pronexus.portal.domain.services.core;

import org.pronexus.portal.app.dtos.CreateUpdateDistrictDto;
import org.pronexus.portal.app.dtos.district.DistrictCriteriaDto;
import org.pronexus.portal.app.response.DistrictRes;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface DistrictService {
    DistrictRes findById(Long id);
    Page<DistrictRes> findAll(DistrictCriteriaDto criteriaDto, Pageable pageable);
    Boolean create(CreateUpdateDistrictDto dto);
    Boolean update(Long id, CreateUpdateDistrictDto dto);
    Boolean delete(Long id);
}