package org.pronexus.portal.domain.services.impl;

import com.salaryadvance.commonlibrary.constants.ApiMessage;
import com.salaryadvance.commonlibrary.exception.BadRequestException;
import com.salaryadvance.commonlibrary.exception.NotFoundException;
import com.salaryadvance.commonlibrary.rest.CommandResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.pronexus.portal.app.dtos.news.CreateNewsCommandDto;
import org.pronexus.portal.app.dtos.news.NewsCriteriaDto;
import org.pronexus.portal.app.dtos.news.NewsModelRes;
import org.pronexus.portal.app.dtos.news.UpdateNewsCommandDto;
import org.pronexus.portal.domain.entities.News;
import org.pronexus.portal.domain.mappers.NewsMapper;
import org.pronexus.portal.domain.repositories.NewsRepository;
import org.pronexus.portal.domain.services.core.NewsService;
import org.pronexus.portal.domain.utils.Constants;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Slf4j
@Service
@RequiredArgsConstructor
public class NewsServiceImpl extends BaseService implements NewsService {

    private final NewsRepository newsRepository;
    private final NewsMapper newsMapper;

    @Override
    @Transactional
    public NewsModelRes find(Long id) {
        News news = newsRepository.findById(id)
                .orElseThrow(() -> new BadRequestException(String.format(Constants.NOT_FOUND_MESSAGE, "News", "id", id)));
        news.setCount(news.getCount() + 1);
        news = newsRepository.save(news);
        return newsMapper.toNewsModelRes(news);
    }

    @Override
    public Page<NewsModelRes> getNews(NewsCriteriaDto criteria, Pageable pageable) {
        // If all criteria are null, return all news
        if (criteria.getTitle() == null && criteria.getCategoryId() == null
                && criteria.getFeatured() == null && criteria.getPublished() == null
                && criteria.getStatus() == null) {
            return newsRepository.findAll(pageable).map(newsMapper::toNewsModelRes);
        }

        // Handle simple criteria cases
        if (criteria.getTitle() != null) {
            return newsRepository.findByTitleContainingIgnoreCase(criteria.getTitle(), pageable)
                    .map(newsMapper::toNewsModelRes);
        }

        if (criteria.getCategoryId() != null && criteria.getPublished() != null && criteria.getFeatured() != null) {
            return newsRepository.findByCategory_IdAndPublishedAndFeatured(
                    criteria.getCategoryId(),
                    criteria.getPublished(),
                    criteria.getFeatured(),
                    pageable
            ).map(newsMapper::toNewsModelRes);
        }

        if (criteria.getCategoryId() != null) {
            return newsRepository.findByCategory_Id(criteria.getCategoryId(), pageable)
                    .map(newsMapper::toNewsModelRes);
        }

        if (criteria.getFeatured() != null) {
            return newsRepository.findByFeatured(criteria.getFeatured(), pageable)
                    .map(newsMapper::toNewsModelRes);
        }

        if (criteria.getPublished() != null) {
            return newsRepository.findByPublished(criteria.getPublished(), pageable)
                    .map(newsMapper::toNewsModelRes);
        }

        // Fallback to all news
        return newsRepository.findAll(pageable).map(newsMapper::toNewsModelRes);
    }

    @Override
    @Transactional
    public CommandResponse<NewsModelRes> create(CreateNewsCommandDto command) {
        News news = newsMapper.toNews(command);
        // Set publishedAt as epoch millis if publishing for the first time
        if (command.getPublished() != null && command.getPublished()) {
            news.setPublishedAt(System.currentTimeMillis());
        }
        News savedNews = newsRepository.save(news);
        return CommandResponse.success(newsMapper.toNewsModelRes(savedNews), ApiMessage.created("News"));
    }

    @Override
    @Transactional
    public CommandResponse<NewsModelRes> update(Long id, UpdateNewsCommandDto command) {
        News news = newsRepository.findById(id)
                .orElseThrow(() -> new NotFoundException(String.format(Constants.NOT_FOUND_MESSAGE, "News", "id", id)));
        // Check if publish status changed from false to true
        boolean publishStatusChanged = !news.getPublished() && (command.getPublished() != null && command.getPublished());
        // Update News entity using mapper
        newsMapper.updateNewsFromDto(news, command);
        // Set publishedAt as epoch millis if publishing for the first time
        if (publishStatusChanged) {
            news.setPublishedAt(System.currentTimeMillis());
        }
        News savedNews = newsRepository.save(news);
        return CommandResponse.success(newsMapper.toNewsModelRes(savedNews), ApiMessage.updated("News"));
    }

    @Override
    @Transactional
    public CommandResponse<Void> delete(Long id) {
        News news = newsRepository.findById(id)
                .orElseThrow(() -> new NotFoundException(String.format(Constants.NOT_FOUND_MESSAGE, "News", "id", id)));
        newsRepository.delete(news);
        return CommandResponse.success(null, ApiMessage.deleted("News"));
    }

    @Override
    public Page<NewsModelRes> getFeaturedNews(Pageable pageable) {
        return newsRepository.findByPublishedAndFeatured(true, true, pageable)
                .map(newsMapper::toNewsModelRes);
    }
}
