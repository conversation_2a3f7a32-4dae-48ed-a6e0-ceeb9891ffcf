package org.pronexus.portal.domain.feign.replies;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ListBankReply {
    @JsonProperty(value = "Data")
    private Data data;

    @JsonProperty(value = "Link")
    private Object link;

    @JsonProperty(value = "Meta")
    private Object meta;

    @JsonProperty(value = "code")
    private String code;
    @lombok.Data
    public static class Data{

        @JsonProperty(value = "BankInfor")
        private List<BankInfo> bankInfo;

        @lombok.Data
        public static class BankInfo{
            @JsonProperty(value = "Id")
            private String id;
            @JsonProperty(value = "Code")
            private String code;
            @JsonProperty(value = "ShortName")
            private String ShortName;
            @JsonProperty(value = "FullName")
            private String FullName;
            @JsonProperty(value = "Description")
            private String Description;
        }
    }
}
