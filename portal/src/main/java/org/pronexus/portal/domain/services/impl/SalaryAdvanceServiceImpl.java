package org.pronexus.portal.domain.services.impl;

import com.salaryadvance.commonlibrary.rest.Response;
import org.pronexus.portal.app.dtos.salaryadvance.SalaryAdvanceSearchDto;
import org.pronexus.portal.app.response.SalaryAdvanceSearchRes;
import org.pronexus.portal.domain.entities.Department;
import org.pronexus.portal.domain.entities.Employee;
import org.pronexus.portal.domain.feign.replies.SalaryAdvanceSearchReply;
import org.pronexus.portal.domain.services.core.SalaryAdvanceService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Service implementation cho việc quản lý ứng lương
 * Xử lý các thao tác liên quan đến ứng lương, t<PERSON>h toán giới hạn ứng lương
 * và các chức năng liên quan đến ứng lương
 */
@Service
public class SalaryAdvanceServiceImpl extends BaseService implements SalaryAdvanceService {
    @Override
    public Page<SalaryAdvanceSearchRes> getSalaryAdvancePage(SalaryAdvanceSearchDto dto, Pageable pageable) {
        Long partnerId = getPartnerIdFromToken();
        List<String> employeeCodeList = new ArrayList<>();
        if (dto.getDepartmentId() != null) {
            List<Employee> employees = employeeRepository.findEmployeesByPartnerIdAndDepartmentId(partnerId,
                    dto.getDepartmentId());
            employeeCodeList = employees.stream().map(Employee::getCode).toList();
            if (dto.getEmployeeCode() != null) {
                if (employeeCodeList.contains(dto.getEmployeeCode())) {
                    employeeCodeList = List.of(dto.getEmployeeCode());
                } else {
                    return new PageImpl<>(new ArrayList<>(), pageable, 0);
                }
            }
        }
        Response<Page<SalaryAdvanceSearchReply>> searchReply = salaryAdvanceClient.getSalaryAdvancePage(
                UUID.randomUUID().toString(),
                employeeCodeList, dto.getEmployeeName(),
                dto.getStartDate(), dto.getEndDate(), partnerId, dto.getAdvanceAmount(), pageable);
        Page<SalaryAdvanceSearchRes> resPage = searchReply.getData().map(this::convertToRes);
        List<String> extractEmployeeIds = resPage.getContent().stream()
                .map(SalaryAdvanceSearchRes::getEmployeeId)
                .filter(Objects::nonNull) // Lọc bỏ giá trị null (nếu có)
                .distinct() // Lọc trùng lặp (nếu cần)
                .toList();

        List<Employee> employeeList = employeeRepository.findEmployeesByUserIdIn(extractEmployeeIds);
        // Map employeeCode -> departmentId
        Map<String, Integer> employeeToDepartmentMap = employeeList.stream()
                .collect(Collectors.toMap(Employee::getUserId, Employee::getDepartmentId,
                        (existing, replacement) -> existing));

        List<Integer> departmentIds = employeeList.stream().map(Employee::getDepartmentId).distinct().toList();
        List<Department> departments = departmentRepository.findAllByIdIn(departmentIds);
        // Map departmentId -> departmentName
        Map<Integer, String> departmentMap = departments.stream()
                .collect(Collectors.toMap(Department::getId, Department::getName));

        // Cập nhật SalaryAdvanceSearchRes với departmentId và departmentName
        List<SalaryAdvanceSearchRes> updatedList = resPage.getContent().stream()
                .peek(res -> {
                    Integer departmentId = employeeToDepartmentMap.get(res.getEmployeeId());
                    res.setDepartmentId(departmentId);
                    res.setDepartmentName(departmentMap.get(departmentId));
                })
                .toList();
        return new PageImpl<>(updatedList, pageable, resPage.getTotalElements());
    }

    @Override
    public List<SalaryAdvanceSearchRes> getListSalaryAdvance(SalaryAdvanceSearchDto dto) {
        List<SalaryAdvanceSearchRes> allResults = new ArrayList<>();
        Pageable pageable = PageRequest.of(0, 500);
        Page<SalaryAdvanceSearchRes> currentPage = getSalaryAdvancePage(dto, pageable);

        while (!currentPage.isEmpty()) {
            allResults.addAll(currentPage.getContent());
            if (currentPage.hasNext()) {
                currentPage = getSalaryAdvancePage(dto, currentPage.nextPageable());
            } else {
                break;
            }
        }
        return allResults;
    }

    private SalaryAdvanceSearchRes convertToRes(SalaryAdvanceSearchReply reply) {
        SalaryAdvanceSearchRes res = new SalaryAdvanceSearchRes();
        res.setEmployeeId(reply.getEmployeeId());
        res.setTransactionId(reply.getTransactionId());
        res.setEmployeeCode(reply.getEmployeeCode());
        res.setEmployeeName(reply.getEmployeeName());
        res.setAdvanceAmount(reply.getAdvanceAmount());
        res.setCreatedAt(reply.getCreatedAt());
        res.setStatus(reply.getStatus());
        return res;
    }
}
