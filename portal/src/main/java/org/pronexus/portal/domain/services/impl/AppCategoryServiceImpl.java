package org.pronexus.portal.domain.services.impl;

import com.salaryadvance.commonlibrary.exception.NotFoundException;
import com.salaryadvance.commonlibrary.utils.TokenUtils;
import lombok.extern.log4j.Log4j2;
import org.pronexus.portal.app.dtos.AppCategoryCriteriaDto;
import org.pronexus.portal.app.dtos.CreateUpdateAppCategoryDto;
import org.pronexus.portal.app.response.AppCategoryRes;
import org.pronexus.portal.domain.entities.AppCategory;
import org.pronexus.portal.domain.entities.type.AppCategoryStatus;
import org.pronexus.portal.domain.mappers.AppCategoryMapper;
import org.pronexus.portal.domain.repositories.AppCategoryRepository;
import org.pronexus.portal.domain.services.core.AppCategoryService;
import org.pronexus.portal.domain.utils.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * Service implementation cho việc quản lý danh mục ứng dụng
 * Xử lý các thao tác liên quan đến danh mục ứng dụng,
 * quản lý trạng thái danh mục và các chức năng liên quan
 */
@Service
@Log4j2
public class AppCategoryServiceImpl implements AppCategoryService {
    @Autowired
    private AppCategoryMapper appCategoryMapper;

    @Autowired
    private AppCategoryRepository appCategoryRepository;

    /**
     * Tạo mới danh mục ứng dụng
     * 
     * @param dto DTO chứa thông tin tạo danh mục
     * @return CommandResponse xác nhận tạo thành công
     */
    @Override
    public Boolean createAppCategory(CreateUpdateAppCategoryDto dto) {
        AppCategory appCategory = appCategoryMapper.toAppCategory(dto);
        appCategoryRepository.save(appCategory);
        return true;
    }

    /**
     * Cập nhật thông tin danh mục ứng dụng
     * 
     * @param id  ID của danh mục
     * @param dto DTO chứa thông tin cập nhật
     * @return CommandResponse xác nhận cập nhật thành công
     */
    @Override
    public Boolean updateAppCategory(Integer id, CreateUpdateAppCategoryDto dto) {
        String username = TokenUtils.getUsername();

        AppCategory appCategory = appCategoryRepository.findAppCategoryById(id);
        if (appCategory == null) {
            throw new NotFoundException(
                    String.format(Constants.NOT_FOUND_MESSAGE, "AppCategory", "AppCategory id", id));
        }
        AppCategory appCategoryUpdate = appCategoryMapper.toAppCategory(dto);
        appCategoryUpdate.setId(appCategory.getId());
        appCategoryUpdate.setCreatedAt(appCategory.getCreatedAt());
        appCategoryUpdate.setCreatedBy(appCategory.getCreatedBy());
        appCategoryUpdate.setUpdatedBy(username);
        appCategoryUpdate.setUpdatedAt(System.currentTimeMillis());
        appCategoryRepository.save(appCategoryUpdate);
        return true;
    }

    /**
     * Lấy thông tin danh mục ứng dụng theo ID
     * 
     * @param id ID của danh mục
     * @return AppCategoryRes chứa thông tin danh mục
     */
    @Override
    public AppCategoryRes detailAppCategory(Integer id) {
        AppCategory appCategory = appCategoryRepository.findAppCategoryById(id);
        if (appCategory == null) {
            throw new NotFoundException(
                    String.format(Constants.NOT_FOUND_MESSAGE, "AppCategory", "AppCategory id", id));
        }
        return appCategoryMapper.toAppCategoryRes(appCategory);
    }

    /**
     * Xóa danh mục ứng dụng
     * 
     * @param id ID của danh mục
     * @return CommandResponse xác nhận xóa thành công
     */
    @Override
    public Boolean deleteAppCategory(Integer id) {
        AppCategory appCategory = appCategoryRepository.findAppCategoryById(id);
        if (appCategory == null) {
            throw new NotFoundException(
                    String.format(Constants.NOT_FOUND_MESSAGE, "AppCategory", "AppCategory id", id));
        }
        // appCategory.setStatus(AppCategoryStatus.DELETED);
        appCategoryRepository.delete(appCategory);
        return true;
    }

    /**
     * Lấy danh sách danh mục ứng dụng có phân trang
     * 
     * @param pageable Thông tin phân trang
     * @return Trang chứa danh sách danh mục
     */
    @Override
    public Page<AppCategoryRes> getAppCategory(Pageable pageable) {
        Page<AppCategory> appCategories = appCategoryRepository.findAll(pageable);
        return appCategories.map(appCategoryMapper::toAppCategoryRes);
    }

    /**
     * Lấy danh sách danh mục ứng dụng có lọc theo điều kiện và phân trang
     * 
     * @param criteria Điều kiện tìm kiếm
     * @param pageable Thông tin phân trang
     * @return Trang chứa danh sách danh mục theo điều kiện
     */
    @Override
    public Page<AppCategoryRes> getAppCategory(AppCategoryCriteriaDto criteria, Pageable pageable) {
        // Build the Specification for filtering at the database level
        Specification<AppCategory> spec = Specification.where(null);

        // Filter by name
        if (criteria.getName() != null && StringUtils.hasText(criteria.getName())) {
            spec = spec.and((root, query, cb) -> cb.like(cb.lower(root.get("name")),
                    "%" + criteria.getName().toLowerCase() + "%"));
        }

        // Filter by section
        if (criteria.getSection() != null && StringUtils.hasText(criteria.getSection())) {
            spec = spec.and((root, query, cb) -> cb.like(cb.lower(root.get("section")),
                    "%" + criteria.getSection().toLowerCase() + "%"));
        }

        // Filter by sectionTitle
        if (criteria.getSectionTitle() != null && StringUtils.hasText(criteria.getSectionTitle())) {
            spec = spec.and((root, query, cb) -> cb.like(cb.lower(root.get("sectionTitle")),
                    "%" + criteria.getSectionTitle().toLowerCase() + "%"));
        }

        // Filter by status
        if (criteria.getStatus() != null) {
            AppCategoryStatus status = null;
            switch (criteria.getStatus()) {
                case 0:
                    status = AppCategoryStatus.INACTIVE;
                    break;
                case 1:
                    status = AppCategoryStatus.ACTIVE;
                    break;
                case 2:
                    status = AppCategoryStatus.DELETED;
                    break;
            }

            if (status != null) {
                AppCategoryStatus finalStatus = status;
                spec = spec.and((root, query, cb) -> cb.equal(root.get("status"), finalStatus));
            }
        }

        // Execute the query with the specification and pageable
        Page<AppCategory> appCategories = appCategoryRepository.findAll(spec, pageable);

        // Convert the results to DTOs
        return appCategories.map(appCategoryMapper::toAppCategoryRes);
    }
}
