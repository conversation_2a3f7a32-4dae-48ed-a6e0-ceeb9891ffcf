package org.pronexus.portal.domain.services.impl;

import com.salaryadvance.commonlibrary.rest.Response;
import lombok.extern.log4j.Log4j2;
import org.pronexus.portal.app.response.BankRes;
import org.pronexus.portal.domain.entities.BankEntity;
import org.pronexus.portal.domain.entities.type.PartnerIntegration;
import org.pronexus.portal.domain.feign.clients.IntegrationClient;
import org.pronexus.portal.domain.feign.replies.ListBankReply;
import org.pronexus.portal.domain.feign.replies.TokenReply;
import org.pronexus.portal.domain.mappers.BankMapper;
import org.pronexus.portal.domain.repositories.BankRepository;
import org.pronexus.portal.domain.services.core.BankService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Service implementation cho việc quản lý ngân hàng
 * X<PERSON> lý các thao tác liên quan đến ngân hàng, thông tin ngân hàng
 * và các chức năng liên quan đến ngân hàng
 */
@Service
@Log4j2
public class BankServiceImpl implements BankService {
    @Autowired
    private BankRepository bankRepository;

    @Autowired
    private IntegrationClient integrationClient;

    @Autowired
    private BankMapper bankMapper;

    /**
     * Lấy danh sách ngân hàng từ hệ thống tích hợp
     * Đồng bộ thông tin ngân hàng từ hệ thống bên ngoài
     * 
     * @return Danh sách thông tin ngân hàng
     */
    @Override
    public List<BankEntity> bankList() {
        Response<TokenReply> tokenReply = integrationClient.getTokenFromBank();
        Response<ListBankReply> listBankReply = integrationClient.getListBank(tokenReply.getData().getAccessToken());

        List<BankEntity> bankEntities = bankMapper.toBankEntities(listBankReply.getData().getData().getBankInfo());
        bankEntities.forEach(v -> v.setPartnerIntegration(PartnerIntegration.PVCOMBANK));
        bankEntities = bankRepository.saveAllAndFlush(bankEntities);
        return bankEntities;
    }

    /**
     * Tìm kiếm thông tin ngân hàng theo tên
     * 
     * @param name Tên ngân hàng cần tìm
     * @return Danh sách thông tin ngân hàng
     */
    @Override
    public List<BankRes> getBankInfo(String name) {
        List<BankRes> bankRes;
        List<BankEntity> bankEntities;
        if (name == null) {
            bankEntities = bankRepository.findAll();
            bankRes = bankMapper.toBankRes(bankEntities);
        } else {
            bankEntities = bankRepository.findBankEntitiesByShortNameContainingIgnoreCase(name);
            bankRes = bankMapper.toBankRes(bankEntities);
        }
        return bankRes;
    }
}
