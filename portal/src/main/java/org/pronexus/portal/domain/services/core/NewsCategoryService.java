package org.pronexus.portal.domain.services.core;

import com.salaryadvance.commonlibrary.rest.CommandResponse;
import org.pronexus.portal.app.dtos.newscategory.CreateNewsCategoryCommandDto;
import org.pronexus.portal.app.dtos.newscategory.NewsCategoryCriteriaDto;
import org.pronexus.portal.app.dtos.newscategory.NewsCategoryModelRes;
import org.pronexus.portal.app.dtos.newscategory.UpdateNewsCategoryCommandDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface NewsCategoryService {
    
    /**
     * Find category by id
     * @param id Category id
     * @return Category information
     */
    NewsCategoryModelRes find(Long id);
    
    /**
     * Find category by slug
     * @param slug Category slug
     * @return Category information
     */
    NewsCategoryModelRes findBySlug(String slug);
    
    /**
     * Get categories with pagination
     * @param criteria Search criteria
     * @param pageable Pagination information
     * @return Page of categories
     */
    Page<NewsCategoryModelRes> getCategories(NewsCategoryCriteriaDto criteria, Pageable pageable);
    
    /**
     * Get all categories in hierarchical structure
     * @return List of top-level categories with their children
     */
    List<NewsCategoryModelRes> getCategoryHierarchy();
    
    /**
     * Create category
     * @param command Category creation command
     * @return Command response with created category
     */
    CommandResponse<NewsCategoryModelRes> create(CreateNewsCategoryCommandDto command);
    
    /**
     * Update category
     * @param id Category id
     * @param command Category update command
     * @return Command response with updated category
     */
    CommandResponse<NewsCategoryModelRes> update(Long id, UpdateNewsCategoryCommandDto command);
    
    /**
     * Delete category
     * @param id Category id
     * @return Command response
     */
    CommandResponse<Void> delete(Long id);
}
