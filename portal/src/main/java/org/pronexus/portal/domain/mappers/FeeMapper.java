package org.pronexus.portal.domain.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.pronexus.portal.app.dtos.CreateUpdateFeeDto;
import org.pronexus.portal.app.response.FeeDetailRes;
import org.pronexus.portal.domain.entities.FeeEntity;

@Mapper(
    componentModel = "spring",
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface FeeMapper {
    FeeEntity toFeeEntity(CreateUpdateFeeDto dto);
    FeeDetailRes toFeeDetailRes(FeeEntity feeEntity);

    // Update các field khác null từ dto vào entity, giữ nguyên field không truyền lên
    void updateFeeEntityFromDto(@MappingTarget FeeEntity entity, CreateUpdateFeeDto dto);
}
