package org.pronexus.user.domain.service.core;

import com.salaryadvance.commonlibrary.rest.CommandResponse;
import org.keycloak.representations.idm.authorization.ScopeRepresentation;

import java.util.List;

public interface ScopeService {
    List<ScopeRepresentation> getAllScopes();
    ScopeRepresentation getScope(String scopeId);
    ScopeRepresentation createScope(ScopeRepresentation data);
    ScopeRepresentation updateScope(String ScopeId, ScopeRepresentation data);
    CommandResponse<Void> deleteScope(String id);
}
