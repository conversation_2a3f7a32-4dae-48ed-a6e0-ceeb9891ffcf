package org.pronexus.user.domain.service.core;

import org.pronexus.user.domain.feign.dto.ZnsResponse;

/**
 * Interface định nghĩa các phương thức của ZnsService
 * Dùng để gửi thông báo qua Zalo Notification Service (ZNS)
 */
public interface ZnsService {

    /**
     * G<PERSON>i thông báo OTP qua Zalo
     *
     * @param phoneNumber Số điện thoại người nhận (đã đăng ký Zalo)
     * @param otpCode Mã OTP
     * @param templateId ID của template thông báo
     * @param otpExpiry Thời gian hết hạn của OTP (phút)
     * @return ZnsResponse chứa kết quả gửi OTP
     */
    ZnsResponse sendOtpNotification(String phoneNumber, String otpCode, String templateId, int otpExpiry);

    /**
     * <PERSON><PERSON><PERSON> thông báo tùy chỉnh qua Zalo
     *
     * @param phoneNumber Số điện thoại người nhận (đã đăng ký <PERSON>)
     * @param templateId ID của template thông báo
     * @param templateData Dữ liệu để điền vào template
     * @return ZnsResponse chứa kết quả gửi thông báo
     */
    ZnsResponse sendCustomNotification(String phoneNumber, String templateId, Object templateData);
}
