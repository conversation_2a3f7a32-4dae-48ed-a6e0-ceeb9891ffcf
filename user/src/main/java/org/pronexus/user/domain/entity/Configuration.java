package org.pronexus.user.domain.entity;

import com.salaryadvance.commonlibrary.persistence.AuditableEntity;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;
import org.pronexus.user.domain.entity.constants.ConfigurationStatus;
import org.pronexus.user.domain.entity.data.JsonMap;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Entity
@Table(name = "configurations", schema = "portal")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@EntityListeners(AuditingEntityListener.class)
public class Configuration extends AuditableEntity {

    @Id
    @Column(name = "config_key", length = 100, nullable = false, unique = true)
    private String key;

    @Type(JsonBinaryType.class)
    @Column(name = "config_value", columnDefinition = "jsonb")
    private JsonMap value;

    @Column(name = "description", length = 500)
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private ConfigurationStatus status;

    @PrePersist
    protected void onCreate() {
        this.status = ConfigurationStatus.ACTIVE;
    }
}
