package org.pronexus.user.domain.feign;

import com.salaryadvance.commonlibrary.rest.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = "salary-advance", url = "${feign.salary-advance.url}")
public interface SalaryAdvanceFeignClient {

    /**
     * Xóa tất cả dữ liệu salary advance của employee
     * <PERSON><PERSON> gồm: transaction, transaction_aud, salary_advance_request,
     * salary_advance_request_aud
     */
    @DeleteMapping("/api/v1/employee/{employeeId}/all-data")
    Response<Boolean> deleteAllEmployeeData(
            @RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
            @PathVariable(name = "employeeId") String employeeId);

    /**
     * Kiểm tra số lượng dữ liệu salary advance của employee
     * Tr<PERSON> về map với key là tên bảng và value là số lượng record
     */
    @GetMapping("/api/v1/employee/{employeeId}/data-count")
    Response<java.util.Map<String, Long>> getEmployeeDataCount(
            @RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
            @PathVariable(name = "employeeId") String employeeId);
}
