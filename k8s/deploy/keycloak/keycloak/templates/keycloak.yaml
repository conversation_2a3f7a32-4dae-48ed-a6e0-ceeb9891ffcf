apiVersion: k8s.keycloak.org/v2alpha1
kind: Keycloak
metadata:
  name: keycloak
spec:
  bootstrapAdmin:
    user:
      secret: keycloak-credentials
  db:
    vendor: postgres
    usernameSecret:
      name: postgresql-credentials
      key: username
    passwordSecret:
      name: postgresql-credentials
      key: password
    host: postgresql.postgres
    database: keycloak
    port: 5432
  http:
    httpEnabled: true
    httpPort: 80
  hostname:
    hostname: http://{{ .Values.hostname }}
    backchannelDynamic: true
    strict: false
  proxy:
    headers: xforwarded
  ingress:
    enabled: true
    className: nginx
  unsupported:
    podTemplate:
      spec:
        volumes:
          - name: yas-themes
            configMap:
              name: yas-themes-configmap
        containers:
          - name: keycloak
            volumeMounts:
              - name: yas-themes
                mountPath: /opt/keycloak/providers
