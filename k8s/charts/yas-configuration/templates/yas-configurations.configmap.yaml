#Configmap of general application config for all microservices
apiVersion: v1
kind: ConfigMap
metadata:
  name: yas-configuration-configmap
  annotations:
    reloader.stakater.com/match: "true"
data:
  application.yaml: |
    {{- toYaml .Values.applicationConfig | nindent 4 }}
  logback.xml: |
    {{- .Values.logbackConfig | nindent 4 }}
---
#Configmap of gateway routes config for bff microservices
apiVersion: v1
kind: ConfigMap
metadata:
  name: yas-gateway-routes-config-configmap
  annotations:
    reloader.stakater.com/match: "true"
data:
  gateway-routes-config.yaml: |
    {{- toYaml .Values.gatewayRoutesConfig | nindent 4 }}
---
#Configmap for backoffice-bff extra
apiVersion: v1
kind: ConfigMap
metadata:
  name: backoffice-bff-extra-configmap
  annotations:
    reloader.stakater.com/match: "true"
data:
  backoffice-bff-extra-config.yaml: |
    {{- toYaml .Values.backofficeBffExtraConfig | nindent 4 }}
---
#Configmap for storefront-bff extra
apiVersion: v1
kind: ConfigMap
metadata:
  name: storefront-bff-extra-configmap
  annotations:
    reloader.stakater.com/match: "true"
data:
  storefront-bff-extra-config.yaml: |
    {{- toYaml .Values.storefrontBffExtraConfig | nindent 4 }}
---
# Configmap for media application config custom
apiVersion: v1
kind: ConfigMap
metadata:
  name: media-application-configmap
  annotations:
    reloader.stakater.com/match: "true"
data:
  media-application.yaml: |
    {{- toYaml .Values.mediaApplicationConfig | nindent 4 }}
---
#Configmap for customer application config custom
apiVersion: v1
kind: ConfigMap
metadata:
  name: customer-application-configmap
  annotations:
    reloader.stakater.com/match: "true"
data:
  customer-application.yaml: |
    {{ toYaml .Values.customerApplicationConfig | nindent 4 }}
---

# Configmap of cart application config custom
apiVersion: v1
kind: ConfigMap
metadata:
  name: cart-application-configmap
  annotations:
    reloader.stakater.com/match: "true"
data:
  cart-application.yaml: |
    {{ toYaml .Values.cartApplicationConfig | nindent 4 }}
---
# Configmap of order application config custom
apiVersion: v1
kind: ConfigMap
metadata:
  name: order-application-configmap
  annotations:
    reloader.stakater.com/match: "true"
data:
  order-application.yaml: |
    {{ toYaml .Values.orderApplicationConfig | nindent 4 }}
---
# Configmap of payment application config custom
apiVersion: v1
kind: ConfigMap
metadata:
  name: payment-application-configmap
  annotations:
    reloader.stakater.com/match: "true"
data:
  payment-application.yaml: |
    {{ toYaml .Values.paymentApplicationConfig | nindent 4 }}
---
# Configmap of payment-paypal application config custom
apiVersion: v1
kind: ConfigMap
metadata:
  name: payment-paypal-application-configmap
  annotations:
    reloader.stakater.com/match: "true"
data:
  payment-paypal-application.yaml: |
    {{ toYaml .Values.paymentPaypalApplicationConfig | nindent 4 }}
---
# Configmap of production application config custom
apiVersion: v1
kind: ConfigMap
metadata:
  name: product-application-configmap
  annotations:
    reloader.stakater.com/match: "true"
data:
  product-application.yaml: |
    {{ toYaml .Values.productApplicationConfig | nindent 4 }}

---
# Configmap of recommendation application config custom
apiVersion: v1
kind: ConfigMap
metadata:
  name: recommendation-application-configmap
  annotations:
    reloader.stakater.com/match: "true"
data:
  recommendation-application.yaml: |
    {{ toYaml .Values.recommendationApplicationConfig | nindent 4 }}

---
# Configmap of sampledata application config custom
apiVersion: v1
kind: ConfigMap
metadata:
  name: sampledata-application-configmap
  annotations:
    reloader.stakater.com/match: "true"
data:
  sampledata-application.yaml: |
    {{ toYaml .Values.sampledataApplicationConfig | nindent 4 }}