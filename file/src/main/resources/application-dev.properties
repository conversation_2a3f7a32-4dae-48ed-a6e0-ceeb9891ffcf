spring.application.name=file
server.servlet.context-path=/file

logging.config=classpath:logback-spring.xml

server.port=8002

spring.security.oauth2.resourceserver.jwt.jwk-set-uri=https://identity.pronexus.vn/realms/pronexus_dev/protocol/openid-connect/certs

spring.jpa.properties.hibernate.dialect = org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.generate-ddl=true
spring.jpa.show-sql=true
spring.datasource.url=******************************************************
spring.datasource.username=postgres
spring.datasource.password=Thanhnx@123$
spring.datasource.hikari.minimumIdle=5
spring.datasource.hikari.maximumPoolSize=200
spring.datasource.hikari.idleTimeout=30000
spring.datasource.hikari.poolName=HikariCP
spring.datasource.hikari.maxLifetime=2000000
spring.datasource.hikari.connectionTimeout=30000

file.directory=/Users/<USER>/Documents/PVCOMBANK/tamungluong/images

spring.servlet.multipart.max-file-size=10MB
cors.allowed-origins=*

# Keycloak Configuration
keycloak.realm=pronexus_dev
keycloak.auth-server-url=https://identity.pronexus.vn
keycloak.client-id=pronexus_dev
keycloak.client-secret=m6tCVoNKq9mvaXfxoY4EXQJu489xOxb2
keycloak.master-username=admin
keycloak.master-password=admin
keycloak.master-client-id=admin-cli