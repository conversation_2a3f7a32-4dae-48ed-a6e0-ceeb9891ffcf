import requests
import json
import uuid
import os
from datetime import datetime, timedelta

# ========== Logic chính ==========
BASE_URL = "https://awsapi-uat.pvcombank.com.vn/v1/ibft"
TOKEN_URL = "https://awsapi-uat.pvcombank.com.vn/partner/oauth2/token"
STATEMENT_URL = f"{BASE_URL}/account-statement"

# Cấu hình authentication
CLIENT_ID = "TGCORP"
CLIENT_SECRET = "GoAA9qtX3pNY4V4adjvo2dYeP0pA11BY"

# Tài khoản test
TEST_ACCOUNT = "************"

# Đ<PERSON>nh nghĩa kết quả mong đợi cho từng test case
EXPECTED_RESULTS = {
    # Test cases cho truy vấn sao kê
    "TC-01": {
        "status_code": 400,
        "check_only_status": True,
        "error_desc": "Data not found"
    },
    "TC-02": {
        "status_code": 200,
        "check_only_status": True
    },
    "TC-03": {
        "status_code": 400,
        "check_only_status": False,
        "error_msg": "pageNumber không được để trống",
        "error_code": "0042"
    },
    "TC-04": {
        "status_code": 400,
        "check_only_status": False,
        "error_msg": "pageNumber phải là số",
        "error_code": "0043"
    },
    "TC-05": {
        "status_code": 400,
        "check_only_status": False,
        "error_desc": "Data not found"
    },
    "TC-06": {
        "status_code": 400,
        "check_only_status": False,
        "error_msg": "pageSize không được để trống",
        "error_code": "0044"
    },
    "TC-07": {
        "status_code": 400,
        "check_only_status": False,
        "error_msg": "pageSize phải là số",
        "error_code": "0045"
    },
    "TC-08": {
        "status_code": 400,
        "check_only_status": False,
        "error_msg": "Hệ thống chỉ cho phép truy vấn tối đa 1000 giao dịch trên một yêu cầu",
        "error_code": "0050"
    },
    "TC-09": {
        "status_code": 400,
        "check_only_status": False,
        "error_msg": "searchStartDate không được để trống",
        "error_code": "0046"
    },
    "TC-10": {
        "status_code": 400,
        "check_only_status": False,
        "error_msg": "searchStartDate không đúng định dạng",
        "error_code": "0047"
    },
    "TC-11": {
        "status_code": 400,
        "check_only_status": False,
        "error_msg": "searchEndDate không được để trống",
        "error_code": "0048"
    },
    "TC-12": {
        "status_code": 400,
        "check_only_status": False,
        "error_msg": "searchEndDate không đúng định dạng",
        "error_code": "0049"
    },
    "TC-13": {
        "status_code": 400,
        "check_only_status": False,
        "error_msg": "Ngày tra cứu lớn hơn ngày hiện tại",
        "error_code": "0052"
    },
    "TC-14": {
        "status_code": 400,
        "check_only_status": False,
        "error_msg": "Ngày bắt đầu phải nhỏ hơn hoặc bằng ngày kết thúc",
        "error_code": "0053"
    },
    "TC-15": {
        "status_code": 400,
        "check_only_status": False,
        "error_msg": "Khoảng thời gian tra cứu tối đa là 90 ngày",
        "error_code": "0055"
    }
}

# Hàm ghi log
def write_log(message, log_file):
    # In ra console
    print(message)

    # Ghi vào file log với UTF-8 encoding
    with open(log_file, "a", encoding="utf-8-sig") as f:
        f.write(message + "\n")
        f.flush()  # Đảm bảo ghi ngay lập tức vào file

def get_token(data, log_file):
    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }
    write_log(f"INPUT: URL={TOKEN_URL}", log_file)
    write_log(f"INPUT: Headers={headers}", log_file)
    write_log(f"INPUT: Data={data}", log_file)

    try:
        response = requests.post(TOKEN_URL, headers=headers, data=data)

        # Log response immediately
        write_log(f"OUTPUT: Status code: {response.status_code}", log_file)
        try:
            json_response = response.json()
            write_log(f"OUTPUT: Response: {json.dumps(json_response, indent=2, ensure_ascii=False)}", log_file)
        except:
            write_log(f"OUTPUT: Response: {response.text}", log_file)

        return response
    except Exception as e:
        write_log(f"Error: {e}", log_file)
        return None

def get_account_statement(token, params, log_file):
    """
    Lấy sao kê tài khoản

    Args:
        token (str): Token xác thực
        params (dict): Các tham số cho API sao kê
    """
    headers = {
        "Authorization": f"Bearer {token}"
    }

    write_log(f"INPUT: URL={STATEMENT_URL}", log_file)
    write_log(f"INPUT: Headers={headers}", log_file)
    write_log(f"INPUT: Params={json.dumps(params, indent=2, ensure_ascii=False)}", log_file)

    try:
        response = requests.get(STATEMENT_URL, headers=headers, params=params)

        # Log response immediately
        write_log(f"OUTPUT: Status code: {response.status_code}", log_file)
        try:
            json_response = response.json()
            write_log(f"OUTPUT: Response: {json.dumps(json_response, indent=2, ensure_ascii=False)}", log_file)
        except:
            write_log(f"OUTPUT: Response: {response.text}", log_file)

        return response
    except Exception as e:
        write_log(f"Error: {e}", log_file)
        return None

def check_expected_result(response, test_case, log_file):
    """Kiểm tra kết quả thực tế so với kết quả mong đợi"""
    if test_case not in EXPECTED_RESULTS:
        write_log(f"CẢNH BÁO: Không tìm thấy kết quả mong đợi cho test case {test_case}", log_file)
        return False

    expected = EXPECTED_RESULTS[test_case]
    test_passed = True
    reasons = []

    # Kiểm tra status code
    if "status_code" in expected:
        if expected["status_code"] == 200 and response.status_code != 200:
            test_passed = False
            reasons.append(f"Status code mong đợi 200 nhưng nhận được {response.status_code}")
        elif expected["status_code"] == 400 and not str(response.status_code).startswith('4'):
            test_passed = False
            reasons.append(f"Status code mong đợi 4xx nhưng nhận được {response.status_code}")

    # Kiểm tra JSON response
    try:
        json_response = response.json()

        # Kiểm tra mã lỗi
        if "error_code" in expected:
            if "code" not in json_response or json_response["code"] != expected["error_code"]:
                test_passed = False
                reasons.append(f"Mã lỗi mong đợi {expected['error_code']} nhưng nhận được {json_response.get('code', 'không có')}")

        # Kiểm tra thông báo lỗi
        if "error_msg" in expected:
            if "message" not in json_response or expected["error_msg"].lower() not in json_response["message"].lower():
                test_passed = False
                reasons.append(f"Thông báo lỗi mong đợi '{expected['error_msg']}' nhưng nhận được '{json_response.get('message', 'không có')}'")

        # Kiểm tra key thành công
        if "success_key" in expected and expected["success_key"] not in json_response:
            test_passed = False
            reasons.append(f"Key thành công ({expected['success_key']}) không tìm thấy trong response")

        # Kiểm tra mô tả lỗi
        if "error_desc" in expected:
            error_found = False
            for value in json_response.values():
                if isinstance(value, str) and expected["error_desc"].lower() in value.lower():
                    error_found = True
                    break
            if not error_found:
                test_passed = False
                reasons.append(f"Mô tả lỗi '{expected['error_desc']}' không tìm thấy trong response")

    except Exception as e:
        if not expected.get("check_only_status", False):
            test_passed = False
            reasons.append(f"Không thể parse JSON response: {str(e)}")

    # Hiển thị kết quả
    result_status = "✅ PASSED" if test_passed else "❌ FAILED"
    write_log(f"\nKẾT QUẢ TEST [{test_case}]: {result_status}", log_file)

    if not test_passed:
        write_log("LÝ DO THẤT BẠI:", log_file)
        for reason in reasons:
            write_log(f"- {reason}", log_file)

    write_log("", log_file)  # Thêm dòng trống
    return test_passed

def handle_response(res, test_case, log_file):
    """Kiểm tra kết quả mong đợi"""
    if res is None:
        write_log(f"\nKẾT QUẢ TEST [{test_case}]: ❌ FAILED (Không thể kết nối API)", log_file)
        return False
    return check_expected_result(res, test_case, log_file)

def test_account_statement(log_file):
    """Thực hiện test các trường hợp của API sao kê"""
    # Khởi tạo biến đếm kết quả
    total_tests = 0
    passed_tests = 0
    skipped_tests = 0
    test_results = []

    write_log("\n===== A. LẤY TOKEN =====", log_file)

    # Test lấy token
    res = get_token({
        "grant_type": "client_credentials",
        "client_id": CLIENT_ID,
        "client_secret": CLIENT_SECRET
    }, log_file)

    if res is None or res.status_code != 200:
        write_log("Không lấy được token, không thể thực hiện các test case.\n", log_file)
        return

    token = res.json().get("access_token")
    if not token:
        write_log("Token không hợp lệ, không thể thực hiện các test case.\n", log_file)
        return

    write_log("\n===== B. TEST API SAO KÊ TÀI KHOẢN =====", log_file)

    # TC-01: Trường hợp không có dữ liệu sao kê
    write_log("\n==== [TC-01] Truy vấn khoảng thời gian không có dữ liệu ====", log_file)
    total_tests += 1

    # Lấy thời gian 1 năm trước
    start_date = (datetime.now() - timedelta(days=365)).strftime("%d-%m-%Y")
    end_date = (datetime.now() - timedelta(days=360)).strftime("%d-%m-%Y")

    params = {
        "pageNumber": 1,
        "pageSize": 20,
        "searchStartDate": start_date,
        "searchEndDate": end_date
    }

    res = get_account_statement(token, params, log_file)
    result = handle_response(res, "TC-01", log_file)
    test_results.append({"test_case": "TC-01", "result": "PASS" if result else "FAIL"})
    if result:
        passed_tests += 1

    # TC-02: Trường hợp có dữ liệu sao kê
    write_log("\n==== [TC-02] Truy vấn với đầy đủ thông tin hợp lệ ====", log_file)
    total_tests += 1

    # Lấy thời gian 7 ngày trước đến hiện tại
    start_date = (datetime.now() - timedelta(days=30)).strftime("%d-%m-%Y")
    end_date = datetime.now().strftime("%d-%m-%Y")

    params = {

        "pageNumber": 1,
        "pageSize": 20,
        "searchStartDate": '01-04-2024',
        "searchEndDate": '01-05-2024'
    }

    res = get_account_statement(token, params, log_file)
    result = handle_response(res, "TC-02", log_file)
    test_results.append({"test_case": "TC-02", "result": "PASS" if result else "FAIL"})
    if result:
        passed_tests += 1

    # TC-03: Bỏ trống pageNumber
    write_log("\n==== [TC-03] Bỏ trống pageNumber ====", log_file)
    total_tests += 1
    params = {

        "pageSize": 20,
        "searchStartDate": start_date,
        "searchEndDate": end_date
    }
    res = get_account_statement(token, params, log_file)
    result = handle_response(res, "TC-03", log_file)
    test_results.append({"test_case": "TC-03", "result": "PASS" if result else "FAIL"})
    if result:
        passed_tests += 1

    # TC-04: pageNumber không đúng định dạng số
    write_log("\n==== [TC-04] pageNumber không đúng định dạng số ====", log_file)
    total_tests += 1
    params = {

        "pageNumber": "abc",
        "pageSize": 20,
        "searchStartDate": start_date,
        "searchEndDate": end_date
    }
    res = get_account_statement(token, params, log_file)
    result = handle_response(res, "TC-04", log_file)
    test_results.append({"test_case": "TC-04", "result": "PASS" if result else "FAIL"})
    if result:
        passed_tests += 1

    # TC-05: pageNumber lớn hơn totalSize/pageSize + 1
    write_log("\n==== [TC-05] pageNumber quá lớn ====", log_file)
    total_tests += 1
    params = {

        "pageNumber": 999999,
        "pageSize": 20,
        "searchStartDate": start_date,
        "searchEndDate": end_date
    }
    res = get_account_statement(token, params, log_file)
    result = handle_response(res, "TC-05", log_file)
    test_results.append({"test_case": "TC-05", "result": "PASS" if result else "FAIL"})
    if result:
        passed_tests += 1

    # TC-06: Bỏ trống pageSize
    write_log("\n==== [TC-06] Bỏ trống pageSize ====", log_file)
    total_tests += 1
    params = {

        "pageNumber": 1,
        "searchStartDate": start_date,
        "searchEndDate": end_date
    }
    res = get_account_statement(token, params, log_file)
    result = handle_response(res, "TC-06", log_file)
    test_results.append({"test_case": "TC-06", "result": "PASS" if result else "FAIL"})
    if result:
        passed_tests += 1

    # TC-07: pageSize không đúng định dạng số
    write_log("\n==== [TC-07] pageSize không đúng định dạng số ====", log_file)
    total_tests += 1
    params = {

        "pageNumber": 1,
        "pageSize": "abc",
        "searchStartDate": start_date,
        "searchEndDate": end_date
    }
    res = get_account_statement(token, params, log_file)
    result = handle_response(res, "TC-07", log_file)
    test_results.append({"test_case": "TC-07", "result": "PASS" if result else "FAIL"})
    if result:
        passed_tests += 1

    # TC-08: pageSize > 1000
    write_log("\n==== [TC-08] pageSize vượt quá 1000 ====", log_file)
    total_tests += 1
    params = {

        "pageNumber": 1,
        "pageSize": 1001,
        "searchStartDate": start_date,
        "searchEndDate": end_date
    }
    res = get_account_statement(token, params, log_file)
    result = handle_response(res, "TC-08", log_file)
    test_results.append({"test_case": "TC-08", "result": "PASS" if result else "FAIL"})
    if result:
        passed_tests += 1

    # TC-09: Bỏ trống searchStartDate
    write_log("\n==== [TC-09] Bỏ trống searchStartDate ====", log_file)
    total_tests += 1
    params = {

        "pageNumber": 1,
        "pageSize": 20,
        "searchEndDate": end_date
    }
    res = get_account_statement(token, params, log_file)
    result = handle_response(res, "TC-09", log_file)
    test_results.append({"test_case": "TC-09", "result": "PASS" if result else "FAIL"})
    if result:
        passed_tests += 1

    # TC-10: searchStartDate sai định dạng
    write_log("\n==== [TC-10] searchStartDate sai định dạng ====", log_file)
    total_tests += 1
    params = {

        "pageNumber": 1,
        "pageSize": 20,
        "searchStartDate": "2024/05/01",
        "searchEndDate": end_date
    }
    res = get_account_statement(token, params, log_file)
    result = handle_response(res, "TC-10", log_file)
    test_results.append({"test_case": "TC-10", "result": "PASS" if result else "FAIL"})
    if result:
        passed_tests += 1

    # TC-11: Bỏ trống searchEndDate
    write_log("\n==== [TC-11] Bỏ trống searchEndDate ====", log_file)
    total_tests += 1
    params = {

        "pageNumber": 1,
        "pageSize": 20,
        "searchStartDate": start_date
    }
    res = get_account_statement(token, params, log_file)
    result = handle_response(res, "TC-11", log_file)
    test_results.append({"test_case": "TC-11", "result": "PASS" if result else "FAIL"})
    if result:
        passed_tests += 1

    # TC-12: searchEndDate sai định dạng
    write_log("\n==== [TC-12] searchEndDate sai định dạng ====", log_file)
    total_tests += 1
    params = {

        "pageNumber": 1,
        "pageSize": 20,
        "searchStartDate": start_date,
        "searchEndDate": "2024/05/15"
    }
    res = get_account_statement(token, params, log_file)
    result = handle_response(res, "TC-12", log_file)
    test_results.append({"test_case": "TC-12", "result": "PASS" if result else "FAIL"})
    if result:
        passed_tests += 1

    # TC-13: Ngày tra cứu lớn hơn ngày hiện tại
    write_log("\n==== [TC-13] Ngày tra cứu lớn hơn ngày hiện tại ====", log_file)
    total_tests += 1
    future_date = (datetime.now() + timedelta(days=1)).strftime("%d-%m-%Y")
    params = {

        "pageNumber": 1,
        "pageSize": 20,
        "searchStartDate": start_date,
        "searchEndDate": future_date
    }
    res = get_account_statement(token, params, log_file)
    result = handle_response(res, "TC-13", log_file)
    test_results.append({"test_case": "TC-13", "result": "PASS" if result else "FAIL"})
    if result:
        passed_tests += 1

    # TC-14: searchStartDate > searchEndDate
    write_log("\n==== [TC-14] Ngày bắt đầu lớn hơn ngày kết thúc ====", log_file)
    total_tests += 1
    params = {

        "pageNumber": 1,
        "pageSize": 20,
        "searchStartDate": end_date,
        "searchEndDate": start_date
    }
    res = get_account_statement(token, params, log_file)
    result = handle_response(res, "TC-14", log_file)
    test_results.append({"test_case": "TC-14", "result": "PASS" if result else "FAIL"})
    if result:
        passed_tests += 1

    # TC-15: Khoảng thời gian > 90 ngày
    write_log("\n==== [TC-15] Khoảng thời gian tra cứu quá 90 ngày ====", log_file)
    total_tests += 1
    start_date_over = (datetime.now() - timedelta(days=100)).strftime("%d-%m-%Y")
    params = {

        "pageNumber": 1,
        "pageSize": 20,
        "searchStartDate": start_date_over,
        "searchEndDate": end_date
    }
    res = get_account_statement(token, params, log_file)
    result = handle_response(res, "TC-15", log_file)
    test_results.append({"test_case": "TC-15", "result": "PASS" if result else "FAIL"})
    if result:
        passed_tests += 1

    # Hiển thị tóm tắt kết quả
    display_summary(passed_tests, total_tests, skipped_tests, test_results, log_file)

def display_summary(passed, total, skipped, test_results, log_file):
    """Hiển thị tóm tắt kết quả test với định dạng cải tiến"""
    write_log("\n\n" + "="*50, log_file)
    write_log("             TÓM TẮT KẾT QUẢ TEST             ", log_file)
    write_log("="*50, log_file)

    # Hiển thị thống kê
    actual_run = total - skipped
    pass_rate = (passed / actual_run * 100) if actual_run > 0 else 0

    write_log(f"  Tổng số test case: {total}", log_file)
    write_log(f"  ✅ Passed:  {passed}/{actual_run} ({pass_rate:.2f}%)", log_file)
    write_log(f"  ❌ Failed:  {actual_run - passed}/{actual_run} ({100-pass_rate:.2f}%)", log_file)
    write_log(f"  ⏭️ Skipped: {skipped}/{total} ({skipped/total*100:.2f}%)", log_file)

    write_log("\nCHI TIẾT KẾT QUẢ:", log_file)
    write_log("-"*50, log_file)
    write_log(f"{'Test Case':<10} {'Kết quả':<10}", log_file)
    write_log("-"*50, log_file)

    for result in test_results:
        status_icon = "✅" if result["result"] == "PASS" else "❌" if result["result"] == "FAIL" else "⏭️"
        write_log(f"{result['test_case']:<10} {status_icon} {result['result']:<10}", log_file)

    write_log("="*50 + "\n", log_file)

def init_log_file():
    """Khởi tạo file log mới"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = f"statement_test_log_{timestamp}.txt"

    # Tạo file log mới với UTF-8 BOM
    with open(log_file, "w", encoding="utf-8-sig") as f:
        f.write(f"===== LOG TEST API SAO KÊ - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} =====\n\n")

    return log_file

# ========== Chạy trực tiếp ==========
if __name__ == "__main__":
    # Khởi tạo file log mới
    log_file = init_log_file()

    write_log("=== TESTING ACCOUNT STATEMENT API ===", log_file)
    write_log("Note: Nếu gặp lỗi 403 Forbidden, vui lòng kiểm tra thông tin xác thực và kết nối mạng.\n", log_file)

    test_account_statement(log_file)

    write_log(f"\nLog đã được ghi vào file: {os.path.abspath(log_file)}", log_file)
