-- <PERSON><PERSON><PERSON> thông tin ứng lương của một nhân viên theo user_id (ví dụ: <PERSON><PERSON><PERSON>)
SELECT * FROM portal.salary_advance_limit WHERE employee_id = '7b15be65-bd46-45c9-ba1c-e6965149c439' ORDER BY year DESC, month DESC;

-- <PERSON><PERSON><PERSON> lịch sử giao dịch ứng lương của một nhân viên
SELECT id, partner_id, employee_id, employee_name, advance_amount, total_fee, actual_received_amount, status, created_at, description 
FROM salary_advance.transaction 
WHERE employee_id = '7b15be65-bd46-45c9-ba1c-e6965149c439' 
ORDER BY created_at DESC;

-- Tổng số tiền ứng trong tháng 5/2025 của một nhân viên
SELECT SUM(advance_amount) AS tong_tien_ung, COUNT(*) AS so_lan
FROM salary_advance.transaction
WHERE employee_id = '7b15be65-bd46-45c9-ba1c-e6965149c439'
AND status IN ('SUCCESS', 'PROCESSING')
AND EXTRACT(MONTH FROM to_timestamp(created_at/1000)) = 5
AND EXTRACT(YEAR FROM to_timestamp(created_at/1000)) = 2025;

-- Lấy trạng thái các giao dịch trong tháng 5/2025
SELECT status, COUNT(*) AS so_lan, SUM(advance_amount) AS tong_tien 
FROM salary_advance.transaction 
WHERE employee_id = '7b15be65-bd46-45c9-ba1c-e6965149c439' 
AND EXTRACT(MONTH FROM to_timestamp(created_at/1000)) = 5 
AND EXTRACT(YEAR FROM to_timestamp(created_at/1000)) = 2025 
GROUP BY status;

-- Lấy thông tin nhân viên theo số điện thoại
SELECT id, code, user_id, phone_number, name 
FROM portal.employee 
WHERE phone_number = '0987000999' OR code = '0987000999' OR user_id = '0987000999';

-- Lấy bản ghi audit ứng lương của nhân viên
SELECT * FROM portal.salary_advance_limit_aud WHERE employee_id = '7b15be65-bd46-45c9-ba1c-e6965149c439' ORDER BY rev DESC;

-- Lấy thông tin giao dịch ứng lương
SELECT * FROM salary_advance.transaction WHERE employee_id = '7b15be65-bd46-45c9-ba1c-e6965149c439' ORDER BY created_at DESC;

SELECT * FROM portal.attendance WHERE employee_code = 'NV08' AND month = 5 AND year = 2025 AND man_day IN ('FULL','HALF');

ALTER TABLE portal.attendance
ALTER COLUMN partner_id SET NOT NULL;


INSERT INTO portal.configurations (
    config_key,
    config_value,
    description,
    status
) VALUES (
    'CUSTOMER_SUPPORT_HOTLINE',
    '{"value": "0968868862"}'::jsonb,
    'Số hotline hỗ trợ khách hàng',
    'ACTIVE'
)
ON CONFLICT (config_key)
DO UPDATE SET
    config_value = '{"value": "0968868862"}'::jsonb,
    description = 'Số hotline hỗ trợ khách hàng'