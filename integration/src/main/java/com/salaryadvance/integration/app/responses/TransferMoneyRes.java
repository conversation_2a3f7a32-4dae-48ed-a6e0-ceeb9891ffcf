package com.salaryadvance.integration.app.responses;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TransferMoneyRes {

    @JsonProperty(value = "Data")
    private Data data;

    @JsonProperty(value = "Meta")
    private Meta meta;

    private String code;

    private ErrorRes errorRes;

    @lombok.Data
    public static class Meta{
        @JsonProperty(value = "RefNum")
        private String refNum;

        @JsonProperty(value = "FtId")
        private String ftId;

        @JsonProperty(value = "SystemTrace")
        private String systemTrace;
    }
    @lombok.Data
    public static class Data{
        @JsonProperty(value = "InstructedAmount")
        private InstructedAmount instructedAmount;

        @JsonProperty(value = "TransId")
        private String transId;

        @JsonProperty(value = "DateTime")
        private String dateTime;

        @JsonProperty(value = "DebitorAccount")
        private Object debitorAccount;

        @JsonProperty(value = "CreditorAccount")
        private Object creditorAccount;

        @lombok.Data
        public static class InstructedAmount{
            @JsonProperty(value = "Amount")
            private Integer amount;
            @JsonProperty(value = "Currency")
            private String currency;
        }
    }
}

