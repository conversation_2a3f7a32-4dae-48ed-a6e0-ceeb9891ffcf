# UAT Environment Configuration
server.port=8088

# PVComBank API Configuration - UAT
pvcom.bank.client.url=https://awsapi-uat.pvcombank.com.vn/
pvcom.bank.grant-type=client_credentials
pvcom.bank.client-id=TGCORP
pvcom.bank.client-secret=521ac63b-c7cc-4350-af48-42ccb5e52c5b

# Keycloak Configuration - UAT
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=https://identity.pronexus.vn/realms/pronexus_uat/protocol/openid-connect/certs
keycloak.realm=pronexus_dev
keycloak.auth-server-url=https://identity.pronexus.vn

# Database Configuration - UAT
spring.datasource.url=******************************************************
spring.datasource.username=postgres
spring.datasource.password=Thanhnx@123$
spring.datasource.hikari.minimumIdle=5
spring.datasource.hikari.maximumPoolSize=200
spring.datasource.hikari.idleTimeout=30000
spring.datasource.hikari.poolName=HikariCP
spring.datasource.hikari.maxLifetime=2000000
spring.datasource.hikari.connectionTimeout=30000

# Logging Configuration - UAT
logging.level.org.springframework.web.client.RestTemplate=DEBUG
logging.level.org.springframework.cloud.openfeign.Feign=DEBUG
