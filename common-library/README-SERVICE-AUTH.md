# Service Authentication Configuration

## Tổng quan

SharedServiceAuthenticationService đư<PERSON>c tự động cấu hình trong common-library với các giá trị mặc định. Các service con không cần phải cấu hình lại trừ khi muốn override.

## Cấu hình mặc định

Common-library đã có sẵn các giá trị mặc định:

```properties
# Keycloak Configuration - Default values
keycloak.realm=pronexus_dev
keycloak.auth-server-url=https://identity.pronexus.vn
keycloak.client-id=pronexus_dev
keycloak.client-secret=m6tCVoNKq9mvaXfxoY4EXQJu489xOxb2
keycloak.master-client-id=admin-cli
keycloak.master-username=admin
keycloak.master-password=admin
```

## Cách sử dụng

### 1. Service muốn sử dụng Service Authentication

Chỉ cần import common-library dependency, ServiceAuthenticationClient sẽ tự động được tạo:

```xml
<dependency>
    <groupId>com.salaryadvance</groupId>
    <artifactId>common-library</artifactId>
    <version>${revision}</version>
</dependency>
```

Sau đó inject và sử dụng:

```java
@Service
@RequiredArgsConstructor
public class MyService {
    
    private final SharedServiceAuthenticationService authService;
    
    public void callOtherService() {
        String token = authService.getServiceToken();
        // Sử dụng token để gọi service khác
    }
}
```

### 2. Service không muốn sử dụng Service Authentication

Thêm vào application.properties:

```properties
keycloak.service-auth.enabled=false
```

Hoặc sử dụng profile no-auth:

```properties
spring.profiles.active=dev,no-auth
```

### 3. Override cấu hình Keycloak

Nếu muốn sử dụng cấu hình Keycloak khác, chỉ cần định nghĩa trong application.properties của service:

```properties
# Override cho environment khác
keycloak.client-id=my_custom_client
keycloak.client-secret=my_custom_secret
keycloak.auth-server-url=https://my-keycloak.com
```

## Lợi ích

1. **Tự động cấu hình**: Không cần cấu hình thủ công ở mỗi service
2. **Giá trị mặc định**: Sử dụng cấu hình chung cho tất cả service
3. **Linh hoạt**: Có thể override hoặc disable khi cần
4. **Tránh lỗi**: Không còn lỗi "Could not resolve placeholder" khi thiếu cấu hình

## Troubleshooting

### Lỗi "Could not resolve placeholder 'keycloak.client-id'"

Nguyên nhân: Service đang cố gắng inject thuộc tính Keycloak nhưng không có cấu hình.

Giải pháp:
1. Thêm cấu hình Keycloak vào application.properties
2. Hoặc disable service authentication: `keycloak.service-auth.enabled=false`
3. Hoặc sử dụng profile no-auth: `spring.profiles.active=dev,no-auth`

### Service không cần authentication

Nếu service không cần gọi API khác hoặc không cần authentication, disable service auth:

```properties
keycloak.service-auth.enabled=false
```
