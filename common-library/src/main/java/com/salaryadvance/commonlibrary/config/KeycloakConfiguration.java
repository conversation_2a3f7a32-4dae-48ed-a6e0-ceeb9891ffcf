//package com.yas.commonlibrary.config;
//
//import lombok.Getter;
//import lombok.Setter;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Configuration;
//
//@Getter
//@Configuration
//public class KeycloakConfiguration {
//    @Value("${keycloak.resource}")
//    private String resource;
//}
