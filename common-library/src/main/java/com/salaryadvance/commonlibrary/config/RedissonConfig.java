package com.salaryadvance.commonlibrary.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.redisson.config.TransportMode;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RedissonConfig {

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();

        // C<PERSON>u hình SingleServerConfig
        SingleServerConfig serverConfig = config.useSingleServer();

        // Đ<PERSON><PERSON> thông tin từ file application.yml hoặc application.properties
        serverConfig.setAddress("redis://*************:6379");
        serverConfig.setPassword(null);  // Nếu không có mật khẩu
        serverConfig.setClientName("salary-advance-service");
        serverConfig.setIdleConnectionTimeout(15000);
        serverConfig.setConnectTimeout(15000);
        serverConfig.setTimeout(10000);
        serverConfig.setRetryAttempts(3);
        serverConfig.setRetryInterval(1500);
        serverConfig.setSubscriptionsPerConnection(5);
        serverConfig.setSubscriptionConnectionMinimumIdleSize(1);
        serverConfig.setSubscriptionConnectionPoolSize(50);
        serverConfig.setConnectionMinimumIdleSize(24);
        serverConfig.setConnectionPoolSize(64);
        serverConfig.setDatabase(0);
        serverConfig.setDnsMonitoringInterval(5000);

        // Codec: Kryo5Codec
        config.setCodec(new org.redisson.codec.Kryo5Codec());

        // Transport mode: NIO
        config.setTransportMode(TransportMode.NIO);

        // Số lượng thread cho Redisson
        config.setThreads(16);
        config.setNettyThreads(32);

        return Redisson.create(config);
    }
}
