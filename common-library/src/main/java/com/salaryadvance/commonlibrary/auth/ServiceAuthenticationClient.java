package com.salaryadvance.commonlibrary.auth;

import com.salaryadvance.commonlibrary.auth.dto.AccessTokenResponse;
import com.salaryadvance.commonlibrary.auth.enums.GrantType;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import java.util.Map;

/**
 * Service Authentication Client cho inter-service communication
 * Cung cấp các phương thức để lấy token cho service-to-service calls
 */
@RequiredArgsConstructor
@Slf4j
public class ServiceAuthenticationClient {

    private final KeycloakServiceFeignClient keycloakServiceFeignClient;

    @Value("${keycloak.client-id:pronexus_dev}")
    private String clientId;

    @Value("${keycloak.client-secret:m6tCVoNKq9mvaXfxoY4EXQJu489xOxb2}")
    private String clientSecret;

    @Value("${keycloak.master-client-id:admin-cli}")
    private String masterClientId;

    @Value("${keycloak.master-username:admin}")
    private String masterUsername;

    @Value("${keycloak.master-password:admin}")
    private String masterPassword;

    /**
     * Lấy service token sử dụng client credentials grant
     * Dùng cho service-to-service authentication
     * 
     * @return Bearer token string
     * @throws ServiceAuthenticationException nếu không thể lấy token
     */
    public String getServiceToken() {
        Map<String, ?> request = Map.of(
                "grant_type", GrantType.CLIENT_CREDENTIALS.getValue(),
                "client_id", clientId,
                "client_secret", clientSecret
        );

        try {
            AccessTokenResponse response = keycloakServiceFeignClient.getToken(request);
            return "Bearer " + response.getToken();
        } catch (FeignException e) {
            log.error("Cannot get service token: {}", e.getMessage());
            throw new ServiceAuthenticationException("Cannot get service token", e);
        }
    }

    /**
     * Lấy master token cho Keycloak admin API calls
     * 
     * @return Bearer token string
     * @throws ServiceAuthenticationException nếu không thể lấy token
     */
    public String getMasterToken() {
        Map<String, ?> request = Map.of(
                "grant_type", GrantType.PASSWORD.getValue(),
                "client_id", masterClientId,
                "username", masterUsername,
                "password", masterPassword
        );

        try {
            AccessTokenResponse response = keycloakServiceFeignClient.getMasterToken(request);
            return "Bearer " + response.getToken();
        } catch (FeignException e) {
            log.error("Cannot get master token: {}", e.getMessage());
            throw new ServiceAuthenticationException("Cannot get master token", e);
        }
    }

    /**
     * Lấy system admin token
     * 
     * @return Bearer token string
     * @throws ServiceAuthenticationException nếu không thể lấy token
     */
    public String getSystemAdminToken() {
        Map<String, ?> request = Map.of(
                "grant_type", GrantType.PASSWORD.getValue(),
                "client_id", clientId,
                "client_secret", clientSecret,
                "username", masterUsername,
                "password", masterPassword
        );

        try {
            AccessTokenResponse response = keycloakServiceFeignClient.getToken(request);
            return "Bearer " + response.getToken();
        } catch (FeignException e) {
            log.error("Cannot get system admin token: {}", e.getMessage());
            throw new ServiceAuthenticationException("Cannot get system admin token", e);
        }
    }

    /**
     * Lấy token cho user cụ thể (dùng cho testing hoặc special cases)
     * 
     * @param username Username
     * @param password Password
     * @return AccessTokenResponse
     * @throws ServiceAuthenticationException nếu không thể lấy token
     */
    public AccessTokenResponse getUserToken(String username, String password) {
        Map<String, ?> request = Map.of(
                "grant_type", GrantType.PASSWORD.getValue(),
                "client_id", clientId,
                "client_secret", clientSecret,
                "username", username,
                "password", password
        );

        try {
            return keycloakServiceFeignClient.getToken(request);
        } catch (FeignException e) {
            log.error("Cannot get user token for username: {}", username);
            throw new ServiceAuthenticationException("Cannot get user token", e);
        }
    }
}
