package com.salaryadvance.commonlibrary.auth.config;

import com.salaryadvance.commonlibrary.auth.KeycloakServiceFeignClient;
import com.salaryadvance.commonlibrary.auth.ServiceAuthenticationClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * Auto-configuration cho Service Authentication
 * Tự động tạo ServiceAuthenticationClient với default values
 * Có thể disable bằng cách set keycloak.service-auth.enabled=false
 */
@Configuration
@EnableFeignClients(basePackages = {
    "com.salaryadvance.commonlibrary.auth",
    "com.salaryadvance.commonlibrary.feign"
})
@ComponentScan(basePackages = {
    "com.salaryadvance.commonlibrary.auth",
    "com.salaryadvance.commonlibrary.feign"
})
@ConditionalOnProperty(name = "keycloak.service-auth.enabled", havingValue = "true", matchIfMissing = true)
@Slf4j
public class ServiceAuthenticationAutoConfiguration {

    /**
     * Tạo ServiceAuthenticationClient bean với default configuration
     * Chỉ tạo khi chưa có bean nào được định nghĩa
     */
    @Bean
    @ConditionalOnMissingBean(ServiceAuthenticationClient.class)
    public ServiceAuthenticationClient serviceAuthenticationClient(KeycloakServiceFeignClient keycloakServiceFeignClient) {
        log.info("Auto-configuring ServiceAuthenticationClient with default Keycloak settings");
        return new ServiceAuthenticationClient(keycloakServiceFeignClient);
    }
}
