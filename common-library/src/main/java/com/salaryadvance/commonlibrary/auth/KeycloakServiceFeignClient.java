package com.salaryadvance.commonlibrary.auth;

import com.salaryadvance.commonlibrary.auth.dto.AccessTokenResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * Feign client để gọi Keycloak authentication APIs
 */
@FeignClient(value = "keycloak-service", url = "${keycloak.auth-server-url:https://identity.pronexus.vn}")
public interface KeycloakServiceFeignClient {

    /**
     * Lấy master token từ Keycloak master realm
     * 
     * @param request Request body chứa grant_type, client_id, username, password
     * @return AccessTokenResponse
     */
    @PostMapping(value = "/realms/master/protocol/openid-connect/token", 
                 produces = MediaType.APPLICATION_JSON_VALUE, 
                 consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    AccessTokenResponse getMasterToken(@RequestBody Map<String, ?> request);

    /**
     * L<PERSON>y token từ realm được cấu hình
     * 
     * @param request Request body chứa grant_type, client_id, client_secret, etc.
     * @return AccessTokenResponse
     */
    @PostMapping(value = "/realms/${keycloak.realm:pronexus_dev}/protocol/openid-connect/token",
                 produces = MediaType.APPLICATION_JSON_VALUE,
                 consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    AccessTokenResponse getToken(@RequestBody Map<String, ?> request);
}
