package com.salaryadvance.commonlibrary.auth.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * Configuration cho Service Authentication và Feign
 * Tự động enable khi common-library được import
 * Sử dụng auto-configuration để tránh lỗi missing properties
 */
@Configuration
@Import(ServiceAuthenticationAutoConfiguration.class)
public class ServiceAuthenticationConfig {
}
