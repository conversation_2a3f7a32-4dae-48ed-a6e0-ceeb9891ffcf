package com.salaryadvance.commonlibrary.exception.base;

public class FileUploadException extends  ApplicationException {

    public FileUploadException(String message, Throwable cause, Object context) {
        super(message, cause, StatusCode.DATA_VALIDATION_ERROR, ErrorSeverity.ERROR, context);
    }

    public FileUploadException(String message, Object context) {
        super(message, StatusCode.DATA_VALIDATION_ERROR, ErrorSeverity.ERROR, context);
    }

    public FileUploadException(String message) {
        super(message, StatusCode.DATA_VALIDATION_ERROR, ErrorSeverity.ERROR, null);
    }
}
