package com.salaryadvance.commonlibrary.excel;

import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExcelData<T> {
    private Map<String, Integer> titles;
    private Map<Integer, T> data;
    private Map<Integer, String> errorData;
    private Object extraData;
    private byte[] returnContent;

    public void setResultAsHeader(HttpServletResponse response) {
        int totalRecord = data.size() + errorData.size();
        int successRecord = data.size();
        int errorRecord = errorData.size();
        response.setHeader("Total-Record", Integer.toString(totalRecord));
        response.setHeader("Success-Record", Integer.toString(successRecord));
        response.setHeader("Error-Record", Integer.toString(errorRecord));
        // Expose headers
        response.setHeader("Access-Control-Expose-Headers", "Total-Record, Success-Record, Error-Record");
    }
}
